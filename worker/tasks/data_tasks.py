import datetime
import os
import time
from io import BytesIO
import random
from functools import reduce
import numpy as np
import pandas as pd

from core_utils.log import logger
from celery_app import app, stock_query, gcs_service, gcsheet_service, memory, redis_cache, logger
from celery import shared_task
from tasks import celery_utils
from explo import talib as ta
from explo import falib as fa
from explo import tdlib as td

from tasks.celery_utils import FPATH, OFFSET, DICT_SHIFT


@app.task(bind=True, autoretry_for=(ValueError, Exception,), retry_kwargs={'max_retries': 2, 'countdown': 10})
def update_index(self, ticker, now_time, daily_update=True, overwrite=False):
    logger.info(f"Updating  raw {ticker.upper()} index")

    df = stock_query.get_historical_index(ticker)

    if daily_update:
        now = now_time.strftime("%Y-%m")
        df = df.query(f'time.str.startswith("{now}")').reset_index(drop=True)
        if not df.empty:
            destination = f"rawdata/index/ymd={now.replace('-', '')}/{ticker}.csv"
            celery_utils.upload_and_rsync(df, destination)

            # Call preprocess
            preprocess_index(ticker, daily_update, destination)

    else:
        now = now_time.strftime("%Y-%m")
        df_lasted = df.query(f'time.str.startswith("{now}")').reset_index(drop=True)
        if not df_lasted.empty:
            destination = f"rawdata/index/ymd={now.replace('-', '')}/{ticker}.csv"
            celery_utils.upload_and_rsync(df_lasted, destination)

        yms = df['time'].transform(lambda x: x[:7]).unique()
        for ym in yms:
            destination = f"rawdata/index/ymd={ym.replace('-', '')}/{ticker}.csv"
            if overwrite or (not gcs_service.file_exists(destination)):
                df_ym = df.query(f'time.str.startswith("{ym}")').reset_index(drop=True).copy()
                celery_utils.upload_and_rsync(df_ym, destination)

        # Call preprocess
        preprocess_index(ticker, daily_update, destination)

    return "done"


# update_index_pe
@app.task(bind=True, autoretry_for=(ValueError, Exception,), retry_kwargs={'max_retries': 2, 'countdown': 10})
def update_enrich_index(self, ticker, now_time, daily_update=True, overwrite=False):
    logger.info(f"Updating  raw index pe")

    df = stock_query.get_enrich_vnindex()

    if daily_update:
        now = now_time.strftime("%Y-%m")
        df = df.query(f'time.str.startswith("{now}")').reset_index(drop=True)
        if not df.empty:
            destination = f"rawdata/index_pe/ymd={now.replace('-', '')}/{ticker}.csv"
            celery_utils.upload_and_rsync(df, destination)

            # Call preprocess data
            preprocess_index_pe(ticker, daily_update, destination)

    else:
        now = now_time.strftime("%Y-%m")
        df_lasted = df.query(f'time.str.startswith("{now}")').reset_index(drop=True)
        if not df_lasted.empty:
            destination = f"rawdata/index_pe/ymd={now.replace('-', '')}/{ticker}.csv"
            celery_utils.upload_and_rsync(df_lasted, destination)

        yms = df['time'].transform(lambda x: x[:7]).unique()
        for ym in yms:
            destination = f"rawdata/index_pe/ymd={ym.replace('-', '')}/{ticker}.csv"
            if overwrite or (not gcs_service.file_exists(destination)):
                df_ym = df.query(f'time.str.startswith("{ym}")').reset_index(drop=True).copy()
                celery_utils.upload_and_rsync(df_ym, destination)

        # Call preprocess data
        preprocess_index_pe(ticker, daily_update, destination)

    return "done"


# update_stock_history
@app.task(bind=True, autoretry_for=(ValueError, Exception,), retry_kwargs={'max_retries': 2, 'countdown': 10})
def update_stock_adjust_price(self, ticker, now_time, daily_update=True):
    logger.info(f"Updating raw stock {ticker.upper()} adjusted price")

    df = stock_query.get_historical_symbol(ticker)
    now = now_time.strftime("%Y-%m-%d")

    if daily_update:
        # delete old preprocess file
        gcs_service.delete_file(f"preprocess/adjusted_price/{ticker}.csv")

        df_q = df.query(f'time.str.startswith("{now}")').reset_index(drop=True)
        if not df_q.empty:
            destination = f"rawdata/adjusted_price/upto_ymd={now.replace('-', '')}/{ticker}.csv"
            gcs_service.upload_from_memory(df, destination)

            # preprocess for calculate
            preprocess_stock_adjust_price(df.tail(240 * 5 + OFFSET), ticker)
    else:
        destination = f"rawdata/adjusted_price/upto_ymd={now.replace('-', '')}/{ticker}.csv"
        gcs_service.upload_from_memory(df, destination)
        # yms = df['time'].transform(lambda x: x[:7]).unique()
        # for ym in yms:
        #     df_ym = df.query(f'time.str.startswith("{ym}")').reset_index(drop=True).copy()
        #     destination = f"rawdata/adjusted_price/upto_ymd={ym.replace('-', '')}/{ticker}.csv"
        #     gcs_service.upload_from_memory(df_ym, destination)

        # preprocess for calculate
        preprocess_stock_adjust_price(df, ticker)

    return "done"


# update_stock_price
@app.task(bind=True, autoretry_for=(ValueError, Exception,), retry_kwargs={'max_retries': 2, 'countdown': 10})
def update_stock_price(self, ticker, now_time, daily_update=True, overwrite=False):
    logger.info(f"Updating raw stock {ticker.upper()} price")

    df = stock_query.get_unadjust_price(ticker)

    if daily_update:
        now = now_time.strftime("%Y-%m")
        df_q = df.query(f'time.str.startswith("{now}")').reset_index(drop=True)

        # delete old file
        # destination = f"rawdata/price/ymd={now.replace('-', '')}/{ticker}.csv"
        # gcs_service.delete_file(destination)
        if not df_q.empty:
            destination = f"rawdata/price/ymd={now.replace('-', '')}/{ticker}.csv"
            gcs_service.upload_from_memory(df_q, destination)

            # preprocess for calculate
            preprocess_stock_price(df.tail(240 * 5 + OFFSET), ticker)

    else:
        yms = df['time'].transform(lambda x: x[:7]).unique()
        for ym in yms:
            destination = f"rawdata/price/ymd={ym.replace('-', '')}/{ticker}.csv"
            if overwrite or (not gcs_service.file_exists(destination)):
                df_ym = df.query(f'time.str.startswith("{ym}")').reset_index(drop=True).copy()
                gcs_service.upload_from_memory(df_ym, destination)

        # preprocess for calculate
        preprocess_stock_price(df, ticker)

    return "done"


# update_financial_report
@app.task(bind=True, autoretry_for=(ValueError, Exception,), retry_kwargs={'max_retries': 2, 'countdown': 30})
def update_financial_report(self, ticker, now_time, daily_update=True, overwrite=False):
    logger.info(
        f"Updating raw financial report for {ticker.upper()} with params daily_update={daily_update}, overwrite={overwrite}")
    now = now_time.strftime("%Y-%m-%d")
    year, length = celery_utils.check_release_quarter_report(now)

    if length == 4 or length == '4':
        update_financial_year_report(ticker, now_time, year=year)

    # check if file exists
    exist = None

    if daily_update:
        destination = f"rawdata/financial_report/quarter={year}Q{length}/{ticker}.csv"
        curr_df = celery_utils.load_file_if_existed(destination)
        if not curr_df.empty:
            is_nan_values = pd.isna(curr_df.values)[0]
            nan_count = is_nan_values[is_nan_values]

            if (len(nan_count) / curr_df.shape[1]) > 0.05:
                exist = curr_df['release_date'].iloc[0]
            else:
                return

        df = stock_query.get_finance_data(ticker, period='quarter')
        df = df[(df['yearReport'] == year) & (df['lengthReport'] == length)].reset_index(drop=True)
        manual_exists = celery_utils.check_exist_file(f"rawdata/manual_financial_report/{ticker}.csv")

        if not df.empty:
            if exist:
                df['release_date'] = exist
            else:
                release = celery_utils.report_date(year, length)
                if not pd.isna(release) and now >= release:
                    df['release_date'] = release
                else:
                    df['release_date'] = now

            destination = f"rawdata/financial_report/quarter={year}Q{length}/{ticker}.csv"
            celery_utils.upload_and_rsync(df, destination)

            # Call preprocess
            preprocess_financial_report(ticker, destination)
            celery_utils.delete_and_rsync(f"rawdata/manual_financial_report/{ticker}.csv")
        elif manual_exists:
            df = celery_utils.load_file_if_existed(f"rawdata/manual_financial_report/{ticker}.csv")
            df['release_date'] = now
            destination = f"rawdata/financial_report/quarter={year}Q{length}/{ticker}.csv"
            celery_utils.upload_and_rsync(df, destination)

            # Call preprocess
            preprocess_financial_report(ticker, destination)


    else:
        # Call API to get financial data
        df = stock_query.get_finance_data(ticker, period='quarter')
        df_year = stock_query.get_finance_data(ticker, period='year')

        df = df.reset_index(drop=True)
        df_year = df_year.reset_index(drop=True)
        df = pd.concat([df, df_year], ignore_index=True)
        df = df.sort_values(['yearReport', 'lengthReport'], ascending=[False, False]).reset_index(drop=True)

        df['release_date'] = np.nan
        for i in range(df.shape[0]):
            year = df['yearReport'].iloc[i]
            length = df['lengthReport'].iloc[i]
            df.loc[i, 'release_date'] = celery_utils.report_date(year, length)
            destination = f"rawdata/financial_report/quarter={year}Q{length}/{ticker}.csv"

            celery_utils.upload_if_not_exist(df.iloc[i:i + 1], destination)

            if overwrite:
                df_current = celery_utils.load_file_if_existed(destination)
                df_upload = df.iloc[i:i + 1]
                df_upload['release_date'] = df_current['release_date'].iloc[0]
                celery_utils.upload_and_rsync(df_upload, destination)

        # Call preprocess
        preprocess_financial_report(ticker, destination)

    return "done"


@app.task(bind=True, autoretry_for=(ValueError, Exception,), retry_kwargs={'max_retries': 2, 'countdown': 10})
def update_financial_year_report(self, ticker, now_time, year):
    logger.info(
        f"Updating raw financial year report {year} for {ticker.upper()}")
    now = now_time.strftime("%Y-%m-%d")
    # check if file exists
    exist = None

    destination_q5 = f"rawdata/financial_report/quarter={year}Q5/{ticker}.csv"
    curr_df = celery_utils.load_file_if_existed(destination_q5)
    if not curr_df.empty:
        is_nan_values = pd.isna(curr_df.values)[0]
        nan_count = is_nan_values[is_nan_values]

        if (len(nan_count) / curr_df.shape[1]) > 0.05:
            exist = curr_df['release_date'].iloc[0]
        else:
            return

    df = stock_query.get_finance_data(ticker, period='year')
    df = df[(df['yearReport'] == year) & (df['lengthReport'] == '5')].reset_index(drop=True)

    if not df.empty:
        df['release_date'] = now
        if exist:
            df['release_date'] = exist

        destination_q5 = f"rawdata/financial_report/quarter={year}Q5/{ticker}.csv"
        celery_utils.upload_and_rsync(df, destination_q5)

        # Call preprocess
        destination_q4 = f"rawdata/financial_report/quarter={year}Q4/{ticker}.csv"

        preprocess_financial_report(ticker, [destination_q4, destination_q5])
        celery_utils.delete_and_rsync(f"rawdata/manual_financial_report/{ticker}.csv")

    return "done"


@app.task(bind=True, autoretry_for=(ValueError, Exception,), retry_kwargs={'max_retries': 2, 'countdown': 10})
def preprocess_index(self, ticker, daily_update, new_obj_path):
    logger.info(f"preprocess index report for {ticker.upper()}")
    # index
    list_obj = celery_utils.get_list_exist_files(prefix=f"rawdata/index/")
    list_obj = [obj for obj in list_obj if ticker in obj]
    list_obj.append(new_obj_path)
    # process list_obj with conditions
    if daily_update:
        offset = 2
        list_obj = list_obj[-(12 * 10 + offset):]

    list_df = []
    for obj in list_obj:
        df = celery_utils.load_file_if_existed(obj)
        # content = gcs_service.download_file_to_memory(obj)
        # df = pd.read_csv(BytesIO(content))
        list_df.append(df)

    df = pd.concat(list_df)
    df = df.rename(columns={'high': 'High', 'low': 'Low', 'open': 'Open', 'close': 'Close', 'volume': 'Volume'})
    df["ticker"] = ticker
    df = df.drop_duplicates(subset=['time'], keep='first').reset_index(drop=True)

    gcs_service.upload_from_memory(df, f"preprocess/index/{ticker}.csv")


@app.task(bind=True, autoretry_for=(ValueError, Exception,), retry_kwargs={'max_retries': 2, 'countdown': 10})
def preprocess_index_pe(self, ticker, daily_update, new_obj_path):
    logger.info(f"preprocess index pe report for {ticker.upper()}")
    # index
    list_obj = celery_utils.get_list_exist_files(prefix=f"rawdata/index_pe/")
    list_obj = [obj for obj in list_obj if ticker in obj]
    list_obj.append(new_obj_path)
    # process list_obj with conditions
    if daily_update:
        offset = 2
        list_obj = list_obj[-(12 * 10 + offset):]

    list_df = []
    for obj in list_obj:
        df = celery_utils.load_file_if_existed(obj)
        # content = gcs_service.download_file_to_memory(obj)
        # df = pd.read_csv(BytesIO(content))
        list_df.append(df)

    df = pd.concat(list_df)
    df.rename(columns={'Pe': 'VNINDEX_PE', 'LNST': 'VNINDEX_LNST', 'trading_session': 'Trading_Session',
                       'volume_session': 'Volume_Session'}, inplace=True)
    df = df[['time', 'Index', 'VNINDEX_PE', 'VNINDEX_LNST', 'Trading_Session', 'Volume_Session']]. \
        sort_values('time', ascending=True)
    df = df.drop_duplicates(subset=['time'], keep='first').reset_index(drop=True)

    gcs_service.upload_from_memory(df, f"preprocess/index_pe/{ticker}.csv")


@app.task(bind=True, autoretry_for=(ValueError, Exception,), retry_kwargs={'max_retries': 2, 'countdown': 10})
def preprocess_stock_adjust_price(self, df, ticker):
    logger.info(f"preprocess adjusted price for {ticker.upper()}")
    ticker_df = celery_utils.get_stock_list()
    ticker_df = ticker_df[ticker_df['ticker'] == ticker]
    # workaround for index
    if ticker.upper() not in ["VNINDEX", "VN30", "HNXINDEX", "HNX30", "UPCOMINDEX"]:
        df.loc[:, ['open', 'high', 'low', 'close']] = df[['open', 'high', 'low', 'close']] * 1000
    df = df.rename(columns={'high': 'High', 'low': 'Low', 'open': 'Open', 'close': 'Close', 'volume': 'Volume'})
    df['ticker'] = ticker.upper()
    df['ICB_Code'] = ticker_df['icb_code4'].values[0] if not ticker_df.empty else np.nan
    df['CT_Code'] = ticker_df['com_type_code'].values[0] if not ticker_df.empty else np.nan
    df['Exchange'] = ticker_df['exchange'].values[0] if not ticker_df.empty else np.nan
    df = df.drop_duplicates(subset=['time'], keep='first').reset_index(drop=True)

    gcs_service.upload_from_memory(df, f"preprocess/adjusted_price/{ticker}.csv")


@app.task(bind=True, autoretry_for=(ValueError, Exception,), retry_kwargs={'max_retries': 2, 'countdown': 10})
def preprocess_stock_price(self, df, ticker):
    logger.info(f"preprocess price for {ticker.upper()}")

    df['Price'] = df['GiaDongCua']
    # workaround for index
    if ticker.upper() not in ["VNINDEX", "VN30", "HNXINDEX", "HNX30", "UPCOMINDEX"]:
        df['Price'] = df['Price'] * 1000

    df = df[['time', 'Price']].reset_index(drop=True)
    df = df.drop_duplicates(subset=['time'], keep='first').reset_index(drop=True)

    gcs_service.upload_from_memory(df, f"preprocess/price/{ticker}.csv")


@app.task(bind=True, autoretry_for=(ValueError, Exception,), retry_kwargs={'max_retries': 1, 'countdown': 10})
def preprocess_financial_report(self, ticker, new_obj_path):
    logger.info(f"preprocess financial report for {ticker.upper()}")

    list_obj = celery_utils.get_list_exist_files_not_change_regular(prefix=f"rawdata/financial_report/")
    list_obj = [obj for obj in list_obj if ticker in obj]
    if isinstance(new_obj_path, str):
        list_obj.append(new_obj_path)
    elif isinstance(new_obj_path, list):
        list_obj.extend(new_obj_path)

    # process string with conditions
    list_df = []
    for obj in list_obj:
        # content = gcs_service.download_file_to_memory(obj)
        # df = pd.read_csv(BytesIO(content))
        df = celery_utils.load_file_if_existed(obj)
        list_df.append(df)
    df = pd.concat(list_df)
    df = df.drop_duplicates(subset=['yearReport', 'lengthReport'], keep='first').reset_index(drop=True)

    celery_utils.upload_and_rsync(df, f"preprocess/financial_report/{ticker}.csv")


@app.task(autoretry_for=(ValueError, Exception,), retry_kwargs={'max_retries': 2, 'countdown': 10})
@redis_cache.cache_method(expiry=1800)
def process_index_indicator(now_time):
    logger.info(f"Process index indicator")

    pd_pe = gcs_service.download_file_to_memory("preprocess/index_pe/VNINDEX.csv")
    pd0 = gcs_service.download_file_to_memory("preprocess/index/VNINDEX.csv")
    pd1 = gcs_service.download_file_to_memory("preprocess/index/VN30.csv")

    pd_pe = pd.read_csv(BytesIO(pd_pe))
    pd0 = pd.read_csv(BytesIO(pd0))
    pd1 = pd.read_csv(BytesIO(pd1))

    pdidx = td.get_pdidx(pd_pe=pd_pe, pd0=pd0, pd1=pd1)

    return pdidx


@app.task(bind=True, autoretry_for=(ValueError, Exception,), retry_kwargs={'max_retries': 2, 'countdown': 10})
def process_stock_indicator(self, ticker: str, now_time, daily_update: bool = True) -> str:
    """
    Update and persist computed indicators for a given stock ticker.

    Pipeline:
      - Load inputs (prices, fundamentals, risk/market).
      - Reconcile working 'Price' column.
      - Compute indicator blocks, merge on 'time'.
      - Add short-horizon ratios (T1W/T2W/T1M/T3M).
      - Write monthly partitions to GCS.
      - Prepare app dataset: merge old+new, dedup, shallow fill last row from previous row.
    """
    logger.info(f"Updating indicator for {ticker}")

    # ---------- Load ----------
    try:
        buf_adj = gcs_service.download_file_to_memory(f"preprocess/adjusted_price/{ticker}.csv")
        buf_unadj = gcs_service.download_file_to_memory(f"preprocess/price/{ticker}.csv")

        unadj_df = pd.read_csv(BytesIO(buf_unadj))
        adj_df = pd.read_csv(BytesIO(buf_adj))
        fa_df = celery_utils.load_file_if_existed(f"preprocess/financial_report/{ticker}.csv")
        risk_df = celery_utils.load_data_with_cache("preprocess/others/risk_indicators.csv")
        mkt_df = celery_utils.load_data_with_cache("preprocess/others/market_indicators.csv")
    except Exception as exc:
        logger.info(f"Stock may not trade today {ticker} | reason={exc}")
        return "done"

    # ---------- Reconcile price frame ----------
    df = adj_df.merge(unadj_df, on="time", how="left")

    # Build 'Price' succinctly:
    # - use existing 'Price' if any, ffill it
    # - fallback to 'Close' where still NA
    df["Price"] = (df["Price"].ffill() if "Price" in df.columns else pd.Series(index=df.index, dtype="float64"))
    df["Price"] = df["Price"].fillna(df["Close"])

    # On the latest row only, prefer max(Close, Price) using a boolean mask (no python-side branching)
    if not df.empty:
        last_mask = df.index == df.index[-1]
        df.loc[last_mask, "Price"] = np.maximum(df.loc[last_mask, "Price"], df.loc[last_mask, "Close"])

    # ---------- Compute indicators ----------
    tech_df = (td.compute_indicator_daily if daily_update else td.compute_indicator_v1)(df, options={"len": 5})
    fa_ind_df = td.compute_fa_indicator_v2(df, fa_df)
    profit_df = td.compute_profit_v1(df)
    add_df = td.compute_additional_indicators(ticker, df, risk_df, mkt_df)
    idx_df = process_index_indicator(now_time.strftime("%Y-%m-%d"))

    # Merge all on 'time' via reduce for clarity
    merged = reduce(lambda l, r: l.merge(r, on="time", how="left"),
                    [tech_df, fa_ind_df, profit_df, idx_df, add_df])

    # ---------- Short-horizon ratios ----------
    for k in ["1W", "2W", "1M", "3M"]:
        n = DICT_SHIFT.get(k)
        if n is not None and "Close" in merged.columns:
            merged[f"T{k}"] = merged["Close"] / merged["Close"].shift(n)

    # ---------- Write monthly partitions ----------
    current_ym = now_time.strftime("%Y-%m")
    if daily_update:
        month_slice = merged.loc[merged["time"].astype(str).str.startswith(current_ym)]
        if not month_slice.empty:
            gcs_service.upload_from_memory(month_slice.reset_index(drop=True),
                                           f"v1/indicator/ym={current_ym.replace('-', '')}/{ticker}.csv")

        # ---------- App-facing dataset ----------
        merged = merged.tail(5).copy()
        # merged.to_csv(f"ticker_temp/{ticker}.csv", index=False)
    else:
        # groupby month key to avoid repeated .query calls
        month_key = merged["time"].astype(str).str[:7]
        for ym, grp in merged.groupby(month_key):
            gcs_service.upload_from_memory(grp.reset_index(drop=True),
                                           f"v1/indicator/ym={ym.replace('-', '')}/{ticker}.csv")

    # ---------- App-facing dataset ----------
    app_old = celery_utils.load_data(ticker, fpath=FPATH)
    app_df = (pd.concat([app_old, merged], ignore_index=True)
              .drop_duplicates(subset="time", keep="last")
              .reset_index(drop=True))

    # Fill last row from previous row in a single pandas op (only for non-'time' cols)
    if len(app_df) >= 2:
        cols = app_df.columns.difference(["time"])
        app_df.loc[app_df.index[-1], cols] = app_df.loc[app_df.index[-1], cols].fillna(
            app_df.loc[app_df.index[-2], cols])

    app_df.to_csv(f"{FPATH}{ticker}.csv", index=False)
    return f"Done process_stock_indicator for {ticker}"


#### Specific test

@app.task(bind=True)
def process_stock_with_range(self, ticker, start_range=0, end_range=None, step=1, amount_data=1):
    if end_range is None:
        end_range = start_range + step

    logger.info(f"Updating indicator for {ticker}")
    # repare data
    try:
        pdraw = gcs_service.download_file_to_memory(f"preprocess/adjusted_price/{ticker}.csv")
        pd_unadj = gcs_service.download_file_to_memory(f"preprocess/price/{ticker}.csv")
        # fa_pdraw = gcs_service.download_file_to_memory(f"preprocess/financial_report/{ticker}.csv")
        fa_pdraw = celery_utils.load_file_if_existed(f"preprocess/financial_report/{ticker}.csv")
        pd_risk = celery_utils.load_data_with_cache(f"preprocess/others/risk_indicators.csv")
        pd_market = celery_utils.load_data_with_cache(f"preprocess/others/market_indicators.csv")

    except:
        return

    # fa_pdraw = pd.read_csv(BytesIO(fa_pdraw))
    pd_unadj = pd.read_csv(BytesIO(pd_unadj))
    pdraw = pd.read_csv(BytesIO(pdraw))

    pdraw = pdraw.merge(pd_unadj, on='time', how='left')
    pdraw = pdraw.tail(240 * 5)

    # add a row future data
    pdidx = process_index_indicator(datetime.datetime.now().strftime("%Y-%m-%d"))
    pdidx = pd.concat([pdidx, pdidx.tail(1)], axis=0).reset_index(drop=True)
    pdidx['time'].iloc[-1] = (datetime.datetime.now() + datetime.timedelta(days=1)).strftime("%Y-%m-%d")
    pdu1 = td.compute_additional_indicators(ticker, pdraw, pd_risk, pd_market)

    df_sample = pdraw.tail(1).copy()
    close = df_sample['Close'].values[0]
    df_sample['time'] = (datetime.datetime.now() + datetime.timedelta(days=1)).strftime("%Y-%m-%d")
    df_sample['Close_Current'] = close
    result = []
    for point in range(start_range, end_range, step):
        if point == 0:
            continue

        df_sample['Close'] = close * (1 + point / 100)
        pd00 = pd.concat([pdraw, df_sample], axis=0).reset_index(drop=True)
        pd00['Price'] = pd00['Price'].ffill().fillna(pd00['Close'])
        # pd00['Price'] = pd00[['Close', 'Price']].max(axis=1)
        if not pd00.empty:
            pd00.loc[pd00.index[-1], 'Price'] = pd00.loc[pd00.index[-1], ['Close', 'Price']].max()

        pdz1 = td.compute_fa_indicator_v2(pd00, fa_pdraw)
        pdx1 = td.compute_indicator_daily(pd00, options={"len": 5})
        # pdy1 = td.compute_profit_v1(pd00)
        # pdxy = pdx1.merge(pdz1, on='time', how='left').merge(pdy1, on='time', how='left').merge(pdidx, on='time',
        #                                                                                     how='left')
        pdxy = pdx1.merge(pdz1, on='time', how='left').merge(pdidx, on='time', how='left').merge(pdu1, on='time',
                                                                                                 how='left')
        pdxy.insert(0, 'Change', point)
        result.append(pdxy.tail(amount_data))

    result = pd.concat(result, axis=0).reset_index(drop=True)
    return result.to_dict(orient='records')


@app.task(bind=True, autoretry_for=(ValueError, Exception,), retry_kwargs={'max_retries': 2, 'countdown': 10})
def get_stock_list(self):
    logger.info(f"Get stock_list")
    ticker_df = gcs_service.download_file_to_memory("rawdata/stock_meta/latest/ticker_list.csv")
    ticker_df = pd.read_csv(BytesIO(ticker_df))
    return ticker_df.to_dict(orient='records')


@shared_task(bind=True, autoretry_for=(ValueError, Exception), retry_kwargs={'max_retries': 1, 'countdown': 10})
def preprocess_stock_risk_indicators(self, ticker, period_years=5):
    try:
        df_market = celery_utils.load_data_with_cache("preprocess/index/VNINDEX.csv")
        df_market = df_market[['time', 'Close']].rename(columns={'Close': 'market'})

        df_0 = celery_utils.load_data(ticker, fpath=FPATH)
        df_0['time'] = pd.to_datetime(df_0['time']).dt.strftime('%Y-%m-%d')
        if df_0.empty:
            return None
        # mask for beta
        # df_0['mask'] = (df_0['Volume_1Y_P50'] * df_0['Price'] / df_0['Inflation_7']) > 1e+9
        df_0['mask'] = df_0['Volume_1Y_P50'] > 20e3

        df_0 = df_0[['time', 'Close', 'mask']].rename(columns={'Close': 'stock'})

        df = df_0[['time', 'stock']].merge(df_market, on='time', how='left')
        df.index = pd.to_datetime(df['time'])
        # df = df.resample('W-FRI').last()
        df = df.sort_index()

        df['stock_return'] = df['stock'].pct_change()
        df['market_return'] = df['market'].pct_change()
        df.dropna(inplace=True)

        # quarters = pd.date_range(start=df.index.min(), end=df.index.max(), freq='Q')
        quarters = df.index.to_period('Q').unique().to_timestamp(how='start')

        index_arr = df.index.values
        stock_return = df['stock_return'].values
        market_return = df['market_return'].values

        results = []
        for end_quarter in quarters:
            start_date = end_quarter - pd.DateOffset(years=period_years)
            mask = (index_arr >= np.datetime64(start_date)) & (index_arr <= np.datetime64(end_quarter))

            if np.sum(mask) < 480:
                results.append({
                    'quarter': str(end_quarter.to_period('Q')),
                    f'{ticker}_downside-beta': None,
                    f'{ticker}_downside-beta_mask': False,
                    f'{ticker}_downside-deviation': None,
                    f'{ticker}_downside-deviation_mask': False,
                    f'{ticker}_beta': None,
                    f'{ticker}_beta_mask': False,
                    f'{ticker}_deviation': None,
                    f'{ticker}_deviation_mask': False
                })
                continue

            stock_ret_window = stock_return[mask]
            market_ret_window = market_return[mask]

            downside_dev = np.sqrt(np.mean(np.minimum(stock_ret_window, 0) ** 2))
            dev = np.std(stock_ret_window)

            if np.var(market_ret_window) != 0:
                cov = np.cov(stock_ret_window, market_ret_window)
                beta = cov[0, 1] / cov[1, 1]
                beta_mask = bool(
                    df_0.iloc[[df_0['time'].searchsorted(end_quarter.strftime("%Y-%m-%d"), side='right') - 1]][
                        'mask'].values[0])
            else:
                beta = None
                beta_mask = False

            down_mask = market_ret_window < 0
            if np.sum(down_mask) < 10:
                downsite_beta = None
                downsite_beta_mask = False
            else:
                stock_down = stock_ret_window[down_mask]
                market_down = market_ret_window[down_mask]
                cov = np.cov(stock_down, market_down)
                downsite_beta = cov[0, 1] / cov[1, 1] if cov[1, 1] != 0 else None
                # downsite_beta_mask = df_0.loc[end_quarter, 'mask'].values[0]
                downsite_beta_mask = bool(
                    df_0.iloc[[df_0['time'].searchsorted(end_quarter.strftime("%Y-%m-%d"), side='right') - 1]][
                        'mask'].values[0])

            results.append({
                'quarter': str(end_quarter.to_period('Q')),
                f'{ticker}_downside-beta': downsite_beta,
                f'{ticker}_downside-beta_mask': downsite_beta_mask,
                f'{ticker}_downside-deviation': downside_dev,
                f'{ticker}_downside-deviation_mask': True,
                f'{ticker}_beta': beta,
                f'{ticker}_beta_mask': beta_mask,
                f'{ticker}_deviation': dev,
                f'{ticker}_deviation_mask': True
            })

        time.sleep(random.randint(1, 5))
        return results

    except Exception as e:
        logger.error(f"[{ticker}] Error in process_stock_risk_indicators: {e}", exc_info=True)
        return None
