import datetime
import os
import random
import time
from io import BytesIO

import numpy as np
import pandas as pd
import celery
import requests

from celery_app import app, stock_query, gcs_service, gcsheet_service, memory, redis_cache, logger
from tasks import celery_utils
from explo import talib as ta
from explo import falib as fa
from explo import tdlib as td

from tasks.celery_utils import FPATH, FA_PATH, TEMP_PATH, OFFSET, DICT_SHIFT


@app.task(bind=True)
def validate_ticker(self, ticker):
    seccond = random.randint(1, 5)
    time.sleep(seccond)
    time_ago = (datetime.datetime.today() - datetime.timedelta(days=10)).strftime('%Y-%m-%d')
    try:
        df = stock_query.get_historical_ticker(ticker, start_date=time_ago)
        # return
        if df.empty:
            return ticker
        return

    except Exception as e:
        print(e)
        if e.args[0] == "Failed to fetch data: 429 - Too Many Requests":
            time.sleep(20)
            raise self.retry(exc=e, countdown=10)
    return ticker


@app.task(bind=True, autoretry_for=(Exception,), retry_kwargs={'max_retries': 3, 'countdown': 10})
def validate_volume_ticker_(self, consider_ticker):
    result = []
    three_month_ago = (datetime.datetime.today() - datetime.timedelta(days=120)).strftime('%Y-%m-%d')
    for ticker in consider_ticker:
        try:
            df = stock_query.get_historical_ticker(ticker, start_date=three_month_ago)
            df['Volume_3M'] = df['volume'].rolling(60, min_periods=1).mean()
            if df['Volume_3M'].tail(1).values < 30e3:
                result.append(ticker)
        except Exception as e:
            print(e)
    return result


@app.task(bind=True, autoretry_for=(Exception,), retry_kwargs={'max_retries': 3, 'countdown': 10})
def update_stock_list(self, update_skip=False, update_monitor=True):
    logger.info(f"Update stock list")

    ticker_exchange = stock_query.symbols_by_exchange()
    ticker_df = stock_query.symbols_by_industries()
    ticker_df = ticker_df.merge(ticker_exchange[['symbol', 'exchange']], on='symbol', how='left')

    old_ticker_df = celery_utils.get_stock_list()
    sell_tickers = gcsheet_service.get_sheet_data(sheet_name="TickerList")['Ticker'].to_list()
    # blacklist_monitor = gcsheet_service.get_sheet_data(sheet_name="TickerBlacklist")['Ticker'].to_list()

    if update_skip:
        consider_ticker = ticker_df['symbol'].unique()
        list_skip = []

        tasks = celery.group(validate_ticker.s(ticker) for ticker in consider_ticker)
        results = tasks.apply_async()

        for async_result in results.results:  # Access individual AsyncResults
            result = async_result.get(disable_sync_subtasks=False)  # Retrieve the result safely
            list_skip.append(result)
        list_skip = [f for f in list_skip if f is not None]
        # list_skip = validate_volume_ticker_(consider_ticker)
    else:
        list_skip = old_ticker_df[old_ticker_df['is_skip'] == True]['ticker'].unique().tolist()

    if update_monitor:
        monitor = [path.replace('preprocess/adjusted_price/', '').replace('.csv', '') for path in
                   celery_utils.get_list_exist_files(prefix='preprocess/adjusted_price')]
    else:
        monitor = old_ticker_df[old_ticker_df['is_monitor'] == True]['ticker'].unique().tolist()

    # ticker have financial report
    # if ticker doesn't have financial report, it will be skiped
    exist_report = [path.replace('preprocess/financial_report/', '').replace('.csv', '') for path in
                    celery_utils.get_list_exist_files(prefix='preprocess/financial_report')]

    # get list of tickers and indexes
    ticker_df['ticker'] = ticker_df['symbol']
    ticker_df = ticker_df[['ticker', 'organ_name', 'com_type_code', 'icb_code4', 'icb_name4', 'exchange']]
    ticker_df[['is_watchlist', 'is_monitor', 'is_skip']] = False

    list_ticker = [f for f in ticker_df['ticker'].unique() if (len(f) == 3)]
    ticker_df = ticker_df[ticker_df['ticker'].isin(list_ticker)]
    ticker_df.reset_index(drop=True, inplace=True)

    for i in range(0, ticker_df.shape[0]):
        ticker = ticker_df['ticker'].iloc[i]

        if ticker not in exist_report:
            ticker_df.loc[i, 'is_skip'] = True
        if ticker in list_skip:
            ticker_df.loc[i, 'is_skip'] = True
        if ticker in sell_tickers:
            ticker_df.loc[i, 'is_watchlist'] = True
        if ticker in monitor:
            ticker_df.loc[i, 'is_monitor'] = True
    # return ticker_df.to_dict(orient='records')
    # ticker_df.to_csv("ticker_list.csv", index=False)
    gcs_service.upload_from_memory(ticker_df, "rawdata/stock_meta/latest/ticker_list.csv")

    return "Update stock list complete"


@app.task(bind=True, autoretry_for=(Exception,), retry_kwargs={'max_retries': 3, 'countdown': 10})
def fill_missing_raw_report(self, ticker):
    df_quarter = stock_query.get_finance_data(ticker, period='quarter')
    df_year = stock_query.get_finance_data(ticker, period='year')
    # df = pd.concat([df_quarter, df_year])
    # df = df.sort_values(['yearReport', 'lengthReport'], ascending=[False, False])
    # df.to_csv("all_report_ADS.csv", index=False)
    reports = []
    for year in df_year['yearReport'].unique():
        if df_quarter[(df_quarter['yearReport'] == year)].empty:
            reports.append(df_year[df_year['yearReport'] == year])

    # for year, group in df_quarter.groupby('yearReport'):
    #     if group.shape[0] == 1 :
    #     reports.append(group
    # pass
    miss_report = pd.concat(reports, ignore_index=True)
    miss_report['lengthReport'] = 4

    df = pd.concat([df_quarter, miss_report]).sort_values(['yearReport', 'lengthReport'],
                                                          ascending=[False, False]).reset_index(drop=True)
    df['release_date'] = None
    for i in range(df.shape[0]):
        year = df['yearReport'].iloc[i]
        length = df['lengthReport'].iloc[i]
        df.loc[i, 'release_date'] = celery_utils.report_date(year, length)
        destination = f"rawdata/financial_report/quarter={year}Q{length}/{ticker}.csv"
        if not gcs_service.file_exists(destination):
            gcs_service.upload_from_memory(df.iloc[i:i + 1], destination)

def _rank_in_the_industry_group(self, df: pd.DataFrame, latest_df: pd.DataFrame):
    # Convert to numeric & replace Inf with NaN
    for col in ["Price", "OShares"]:
        latest_df[col] = pd.to_numeric(latest_df[col], errors="coerce")
    latest_df[["Price", "OShares"]] = latest_df[["Price", "OShares"]].replace([np.inf, -np.inf], np.nan)

    # Calculate Market Cap
    latest_df["Market_Cap"] = latest_df["Price"] * latest_df["OShares"]

    # Rank based only on valid Market_Cap values
    latest_df["MCap_rank"] = latest_df.groupby("ICB_Code")["Market_Cap"] \
        .rank(method="first", ascending=False)

    # Denominator is the count of valid values in each group
    latest_df["Group_size"] = latest_df.groupby("ICB_Code")["Market_Cap"] \
        .transform(lambda s: s.notna().sum())

    # Build Rank_str safely with nullable Int64
    rank_str = latest_df["MCap_rank"].round().astype("Int64").astype(str).replace("<NA>", "—")
    size_str = latest_df["Group_size"].astype("Int64").astype(str).replace("<NA>", "—")
    latest_df["Rank_str"] = rank_str + "/" + size_str

    # Map back to the original df
    df["group_rank_str"] = df["ticker"].map(latest_df.set_index("ticker")["Rank_str"])

@app.task(bind=True, autoretry_for=(Exception,), retry_kwargs={'max_retries': 3, 'countdown': 10})
def aggregate_market_data(self, daily_update=True):
    """
    Aggregate all ticker indicator data, compute industry-level PE and market capitalization metrics,
    and upload the result to Google Cloud Storage.

    Parameters
    ----------
    daily_update : bool, default True
        If True, only recalculate and append the latest date's data to the existing industry indicators file.
        If False, perform a full recalculation for all historical data.

    Returns
    -------
    str
        Status message indicating completion and the number of industry-date rows processed.
    """
    logger.info(f"Starting industry indicator aggregation (daily_update={daily_update})")

    # 1. Load ticker list and metadata (for ICB_Code, etc.)
    ticker_meta = celery_utils.get_stock_list()
    ticker_meta_df = pd.DataFrame(ticker_meta)
    tickers = ticker_meta_df[~ticker_meta_df['is_skip']]['ticker'].unique().tolist()

    # 2. Load all ticker indicator data from ticker_v1a/
    dfs = []
    for ticker in tickers:
        try:
            df = celery_utils.load_data(ticker, fpath=FPATH,
                                        use_cols=['time', 'ticker', 'Close', 'Volume', 'PE', 'ICB_Code',
                                                  'Risk_Rating', 'OShares', 'Price'])
            if daily_update:
                df = df.tail(50)

            dfs.append(df)
        except Exception as e:
            logger.warning(f"Failed to load {ticker}: {e}")
            continue

    if not dfs:
        logger.warning("No ticker data loaded. Exiting.")
        return "No data to process."

    all_df = pd.concat([df for df in dfs if not df.empty], ignore_index=True)
    all_df = all_df.merge(ticker_meta_df[['ticker', 'exchange']], on='ticker', how='left')

    post_df = all_df[['time']].drop_duplicates().sort_values("time").reset_index(drop=True)

    # Ensure correct dtypes and handle missing values
    # all_df['time'] = pd.to_datetime(all_df['time'], errors='coerce')
    all_df = all_df.dropna(subset=['time', 'ICB_Code'])
    all_df['Market_Cap'] = all_df['Price'] * all_df['OShares']

    # Calculate PE for each ICB_Code
    pe_pivot = (
        all_df.groupby(["time", "ICB_Code"], as_index=False)["PE"].mean()
        .pivot(index="time", columns="ICB_Code", values="PE")
    )
    pe_pivot.columns = [f"PE_{icb}_mean" for icb in pe_pivot.columns]

    num = all_df.assign(num=all_df['PE'] * all_df['Market_Cap']).dropna(subset=['num', 'Market_Cap']).groupby(
        ['time', 'ICB_Code'])['num'].sum()
    den = all_df.groupby(['time', 'ICB_Code'])['Market_Cap'].sum()
    pe_pivot_weight = (num / den).unstack('ICB_Code')
    pe_pivot_weight.columns = [f"PE_{icb}_weight" for icb in pe_pivot_weight.columns]

    # Calculate Capitalization for exchange
    cap_by_time = all_df.groupby("time", as_index=True)["Market_Cap"].sum()
    cap_by_time = cap_by_time.rename("Capitalization")

    # Calculate Capitalization for each exchange
    cap_by_exchange = (
        all_df.groupby(["time", "exchange"], as_index=False)["Market_Cap"].sum()
        .pivot(index="time", columns="exchange", values="Market_Cap")
    )
    cap_by_exchange.columns = [f"Capitalization_{ex}" for ex in cap_by_exchange.columns]

    # Merge tất cả vào post_df theo time
    final_df = (
        post_df.set_index("time")
        .join(pe_pivot, how="left")
        .join(pe_pivot_weight, how="left")
        .join(cap_by_time, how="left")
        .join(cap_by_exchange, how="left")
        .reset_index()
    )

    #
    # 4. Update strategy
    gcs_path = "preprocess/others/market_indicators.csv"
    if daily_update:
        now = datetime.datetime.now().strftime("%Y-%m")
        final_df = final_df.query(f'time.str.startswith("{now}")').reset_index(drop=True)

        # Load existing, remove latest date, append new latest date
        existing = celery_utils.load_file_if_existed(gcs_path)
        # existing['time'] = pd.to_datetime(existing['time'], errors='coerce')
        final_df = (pd.concat([existing, final_df], ignore_index=True).
                    drop_duplicates(subset='time', keep='last').reset_index(drop=True))

    # 5. Output & Storage
    final_df = final_df.sort_values(['time']).reset_index(drop=True)
    celery_utils.upload_and_rsync(final_df, gcs_path)
    logger.info(f"Uploaded industry indicators to {gcs_path} ({len(final_df)} rows)")

    return f"Industry indicators aggregation complete. Rows: {len(final_df)}"
