import time
from datetime import datetime
import pandas as pd
from celery_app import app, logger, stock_query
from tasks import update_index, update_stock_adjust_price, update_stock_price, update_financial_report, \
    update_enrich_index, \
    process_stock_indicator, fill_missing_raw_report, update_stock_list, preprocess_stock_risk_indicators
from celery import chain, group, chord
from tasks import celery_utils
from celery.schedules import crontab
from functools import reduce
import numpy as np

temp_path = "temp/"
offset = 100


# Define a periodic task
# @app.on_after_configure.connect
# def setup_periodic_tasks(sender, **kwargs):
# Calls 'my_periodic_task' every 10 seconds
# sender.add_periodic_task(10.0, my_periodic_task.s(), name='add every 10 seconds')


# @app.on_after_configure.connect
# def setup_periodic_tasks(sender, **kwargs):
#     # sender.add_periodic_task(10.0, my_task.s(), name='Run every 10 seconds')
#     sender.add_periodic_task(
#         crontab(hour='21', minute='30', day_of_week='1-5'),
#         update_financial_reports.s(daily_update=True, overwrite=False),
#         name='Update daily financial report'
#     )


# @app.task
# def my_periodic_task():
#     print("This task runs every 10 seconds")
#
#
# @app.task(bind=True)
# def long_task(self, total_steps):
#     """A long-running task that updates its progress."""
#     for i in range(total_steps):
#         # Simulate work
#         time.sleep(1)
#
#         # Update task progress
#         self.update_state(state='PROGRESS', meta={'current': i, 'total': total_steps})
#
#     return {'status': 'Task completed!', 'total_steps': total_steps}


@app.task(bind=True)
def update_financial_reports(self, daily_update=True, overwrite=False):
    logger.info(
        f"Download and update financial_reports data with params daily_update={daily_update}, overwrite={overwrite}")
    now = datetime.now()
    # warm up cache
    celery_utils.get_list_exist_files_not_change_regular(prefix=f"rawdata/financial_report/")

    df_ticker = celery_utils.get_stock_list()
    # list_ticker = [ticker['ticker'] for _, ticker in df_ticker.iterrows() if not ticker['is_skip']]
    list_ticker = [ticker['ticker'] for _, ticker in df_ticker.iterrows()]
    for ticker in list_ticker:
        time.sleep(2)
        update_financial_report.apply_async(
            kwargs={'ticker': ticker, 'now_time': now, 'daily_update': daily_update, 'overwrite': overwrite})


@app.task(bind=True)
def update_stocks(self, daily_update):
    logger.info(f"Download and update stock data")
    now = datetime.now()

    # list_ticker = ["HPG", "MWG", "MBB"]
    df_ticker = celery_utils.get_stock_list()
    list_ticker = [ticker['ticker'] for _, ticker in df_ticker.iterrows() if not ticker['is_skip']]
    list_ticker.extend(["VNINDEX"])
    update_tasks = []
    for ticker in list_ticker:
        update_tasks.append(
            update_stock_price.s(ticker=ticker, now_time=now, daily_update=daily_update).set(time_limit=60))
        update_tasks.append(
            update_stock_adjust_price.s(ticker=ticker, now_time=now, daily_update=daily_update).set(time_limit=60))

    # Wait for all tasks to complete
    job = group(update_tasks)
    result = job.apply_async()
    while not result.ready():
        time.sleep(5)


# process_stock_indicator.s(ticker=obj, now_time=now, daily_update=daily_update).set(time_limit=500))

@app.task(bind=True)
def update_indexes(self, daily_update):
    logger.info(f"Download and update index data")
    now = datetime.now()
    # warm up cache
    celery_utils.get_list_exist_files(prefix=f"rawdata/index/")

    list_index = ["VNINDEX", "VN30", "HNXINDEX", "HNX30", "UPCOMINDEX"]

    for index in list_index:
        update_index.apply_async(
            kwargs={'ticker': index, 'now_time': now, 'daily_update': daily_update}, time_limit=60)

        if index == "VNINDEX":
            update_enrich_index.apply_async(
                kwargs={'ticker': index, 'now_time': now, 'daily_update': daily_update}, time_limit=60
            )
        time.sleep(1)


@app.task(bind=True)
def update_data(self, daily_update):
    update_stocks(daily_update)
    time.sleep(5)
    update_indexes(daily_update)


@app.task(bind=True)
def compute_indicators(self, daily_update):
    logger.info(f"Compute indicators")
    now = datetime.now()
    # list_ticker = ["HPG", "MWG", "MBB"]
    df_ticker = celery_utils.get_stock_list()
    list_ticker = [ticker['ticker'] for _, ticker in df_ticker.iterrows() if not ticker['is_skip']]
    list_ticker.extend(["VNINDEX"])

    update_tasks = []
    for obj in list_ticker:
        update_tasks.append(
            process_stock_indicator.s(ticker=obj, now_time=now, daily_update=daily_update).set(time_limit=500))
    job = group(update_tasks)
    result = job.apply_async(propagate=False)
    while not result.ready():
        time.sleep(5)


@app.task(bind=True)
def pipeline(self, daily_update):
    result = update_data.apply_async(args=(daily_update,))
    logger.info(f"Waiting for update data")
    while not result.ready():
        time.sleep(2)
    logger.info(f"Waiting for update indicators")
    result = compute_indicators.apply_async(args=(daily_update,))
    while not result.ready():
        time.sleep(2)
    # update stock list with new data
    result = update_stock_list.apply_async(kargs={'update_skip': False, 'update_monitor': True})
    while not result.ready():
        time.sleep(2)

    return


# periodic_tasks
@app.task(bind=True)
def periodic_fill_miss_report(self):
    df_ticker = celery_utils.get_stock_list()
    # list_ticker = [ticker['ticker'] for _, ticker in df_ticker.iterrows() if not ticker['is_skip']]
    list_ticker = [ticker['ticker'] for _, ticker in df_ticker.iterrows()]
    for ticker in list_ticker:
        time.sleep(1)
        fill_missing_raw_report.apply_async(kwargs={'ticker': ticker})


@app.task
def finalize_risk_indicators(results):
    logger.info("Chord completed. Number of results: %s", len(results))

    def merge_on_quarter(df1, df2):
        return pd.merge(df1, df2, on='quarter', how='outer')

    dfs = [pd.DataFrame(r) for r in results if (r and not isinstance(r, Exception))]
    if not dfs:
        raise ValueError("No valid results to process.")

    final_df = reduce(merge_on_quarter, dfs)
    final_df = final_df.sort_values(by='quarter')

    metrics = ['downside-beta', 'downside-deviation', 'beta', 'deviation']
    df_bins = {}

    for metric in metrics:
        df_bins[metric] = celery_utils.apply_percentile_bins(final_df, prefix=metric, n_bins=5)

    for metric, df_metric in df_bins.items():
        for col in df_metric.columns:
            if col == 'quarter':
                continue
            ids = df_metric[~pd.isna(df_metric[col])].index[:12]
            df_metric.loc[ids, col] = np.ceil((df_metric.loc[ids, col] + 3) / 2).astype(int)

    for metric in metrics:
        final_df = final_df.merge(df_bins[metric], on='quarter', how='left')

    celery_utils.upload_and_rsync(final_df, 'preprocess/others/risk_indicators.csv')
    return "Final risk indicators updated."


@app.task()
def update_risk_indicators():
    logger.info("Download and update_risk_indicators")

    df_market = stock_query.get_historical_index('VNINDEX')
    df_market = df_market.rename(columns={
        'high': 'High', 'low': 'Low', 'open': 'Open',
        'close': 'Close', 'volume': 'Volume'
    })
    df_market["ticker"] = 'VNINDEX'
    df_market = df_market.drop_duplicates(subset=['time']).reset_index(drop=True)
    celery_utils.upload_and_rsync(df_market, "preprocess/index/VNINDEX.csv")

    df_ticker = celery_utils.get_stock_list()
    list_ticker = [ticker['ticker'] for _, ticker in df_ticker.iterrows()]
    list_ticker.extend(["VNINDEX", "VN30", "HNXINDEX", "HNX30", "UPCOMINDEX"])

    # list_ticker = ['HPG', 'MWG', 'MBB', 'DPG', 'HHS', 'FPT', 'VCB', 'ACB', 'TCH', 'VGG']
    # update_tasks = []
    #
    # for ticker in list_ticker:
    #     update_tasks.append(process_stock_risk_indicators.s(ticker=ticker, period_years=5))
    # job = group(update_tasks)
    # results = job.apply_async().get()
    # finalize_risk_indicators(results)

    tasks = [
        preprocess_stock_risk_indicators.s(ticker=ticker, period_years=5).set(time_limit=120)
        for ticker in list_ticker
    ]

    # Trigger chord
    return chord(tasks)(finalize_risk_indicators.s())
