"""
Performance testing script for Simulation_v2 optimizations.

This script validates that:
1. Optimized version produces same results as original
2. Performance improvements are significant
3. Memory usage is reduced

Usage:
    python test_performance_optimization.py
"""

import os
import sys
import time
import json
import tracemalloc
import pandas as pd
import numpy as np

# Setup paths
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)

from webui.utils_v2 import Simulation_v2


def format_time(seconds):
    """Format seconds to human-readable string."""
    if seconds < 60:
        return f"{seconds:.2f}s"
    elif seconds < 3600:
        return f"{seconds/60:.2f}m"
    else:
        return f"{seconds/3600:.2f}h"


def format_memory(bytes_size):
    """Format bytes to human-readable string."""
    for unit in ['B', 'KB', 'MB', 'GB']:
        if bytes_size < 1024.0:
            return f"{bytes_size:.2f} {unit}"
        bytes_size /= 1024.0
    return f"{bytes_size:.2f} TB"


def test_simulation_performance(df_proba, iterate_values=[10, 50], num_proc=10):
    """
    Test simulation performance with different iteration counts.
    
    Args:
        df_proba: Input DataFrame with probability scores
        iterate_values: List of iteration counts to test
        num_proc: Number of parallel processes
    
    Returns:
        dict: Performance metrics
    """
    
    simulate_config = {
        'initial_amount': 1e9,
        'cutloss': 0.2,
        'cutloss_duration': 15,
        'ratio_nav': 1.0,
        'ratio_deal': 0.1,
        'ratio_deal_volume': 0.1,
        'review_frequency': 'monthly',
        'fee_buy_rate': 0.001,
        'fee_sell_rate': 0.002,
        'score_sell': 0,
        'score_buy': 1,
        'gamma': 1,
        'min_ratio_deal_nav': 0.01,
        'verbose': False,
    }
    
    score_config = {
        "score_col": "score",
        "proba_col": "proba",
        "step_round": 0.2,
        'calibrate_kind': None,
        'use_temperature': False,
        'temp_mode': 'brier',
        'percentiles': (0, 20, 40, 60, 80, 100),
        'score_knots': None,
        'lift_target_rates': None,
        'base_rate': None,
        'clip_range': (-1.0, 3.0),
    }
    
    results = {}
    
    print("=" * 80)
    print("PERFORMANCE TEST - Simulation_v2 Optimizations")
    print("=" * 80)
    print(f"\nInput data shape: {df_proba.shape}")
    print(f"Number of processes: {num_proc}")
    print(f"Test iterations: {iterate_values}")
    print("\n" + "-" * 80)
    
    for iterate in iterate_values:
        print(f"\n🔄 Testing with iterate={iterate}...")
        
        # Start memory tracking
        tracemalloc.start()
        start_time = time.time()
        
        try:
            # Run simulation
            simulation = Simulation_v2(
                simulate_config, 
                score_config, 
                start_date='2025-06-01',
                num_proc=num_proc
            )
            result = simulation.run_fast(df_proba, iterate=iterate)
            
            # Measure time and memory
            elapsed_time = time.time() - start_time
            current_mem, peak_mem = tracemalloc.get_traced_memory()
            tracemalloc.stop()
            
            # Store results
            results[iterate] = {
                'elapsed_time': elapsed_time,
                'peak_memory_mb': peak_mem / (1024 * 1024),
                'success': True,
                'result_keys': list(result.keys()) if result else [],
                'cagr': result.get('CAGR', None) if result else None
            }
            
            # Print results
            print(f"  ✅ Success!")
            print(f"  ⏱️  Time: {format_time(elapsed_time)}")
            print(f"  💾 Peak Memory: {format_memory(peak_mem)}")
            if result and 'CAGR' in result:
                print(f"  📊 CAGR: {result['CAGR']:.4f}")
            
        except Exception as e:
            elapsed_time = time.time() - start_time
            tracemalloc.stop()
            
            results[iterate] = {
                'elapsed_time': elapsed_time,
                'success': False,
                'error': str(e)
            }
            
            print(f"  ❌ Failed!")
            print(f"  Error: {str(e)}")
    
    print("\n" + "=" * 80)
    print("SUMMARY")
    print("=" * 80)
    
    # Calculate speedup if multiple iterations tested
    if len(iterate_values) > 1:
        times = [results[i]['elapsed_time'] for i in iterate_values if results[i]['success']]
        if len(times) > 1:
            # Estimate time per iteration
            time_per_iter = [results[i]['elapsed_time'] / i for i in iterate_values if results[i]['success']]
            avg_time_per_iter = np.mean(time_per_iter)
            print(f"\n⚡ Average time per iteration: {format_time(avg_time_per_iter)}")
            
            # Extrapolate for larger runs
            for target in [100, 500, 1000]:
                estimated = avg_time_per_iter * target
                print(f"   Estimated for iterate={target}: {format_time(estimated)}")
    
    return results


def test_correctness(df_proba_small):
    """
    Test that optimized version produces correct results.
    
    Args:
        df_proba_small: Small sample of input data for quick testing
    
    Returns:
        bool: True if tests pass
    """
    print("\n" + "=" * 80)
    print("CORRECTNESS TESTS")
    print("=" * 80)
    
    simulate_config = {
        'initial_amount': 1e9,
        'cutloss': 0.2,
        'cutloss_duration': 15,
        'ratio_nav': 1.0,
        'ratio_deal': 0.1,
        'ratio_deal_volume': 0.1,
        'review_frequency': 'monthly',
        'fee_buy_rate': 0.001,
        'fee_sell_rate': 0.002,
        'score_sell': 0,
        'score_buy': 1,
        'gamma': 1,
        'min_ratio_deal_nav': 0.01,
        'verbose': False,
    }
    
    score_config = {
        "score_col": "score",
        "proba_col": "proba",
        "step_round": 0.2,
        'calibrate_kind': None,
        'use_temperature': False,
        'temp_mode': 'brier',
        'percentiles': (0, 20, 40, 60, 80, 100),
        'score_knots': None,
        'lift_target_rates': None,
        'base_rate': None,
        'clip_range': (-1.0, 3.0),
        'step_round': 0.2
    }
    
    try:
        print("\n✓ Testing basic functionality...")
        simulation = Simulation_v2(simulate_config, score_config, start_date='2025-06-01', num_proc=2)
        result = simulation.run_fast(df_proba_small, iterate=5)
        
        # Check result structure
        assert isinstance(result, dict), "Result should be a dictionary"
        assert 'CAGR' in result, "Result should contain CAGR"
        assert isinstance(result['CAGR'], (int, float)), "CAGR should be numeric"
        
        print("  ✅ Basic functionality test passed")
        
        # Test determinism (same seed should give same result)
        print("\n✓ Testing determinism...")
        np.random.seed(42)
        result1 = simulation.run_fast(df_proba_small, iterate=3)
        
        np.random.seed(42)
        result2 = simulation.run_fast(df_proba_small, iterate=3)
        
        # CAGR should be similar (may have small floating point differences)
        if 'CAGR' in result1 and 'CAGR' in result2:
            diff = abs(result1['CAGR'] - result2['CAGR'])
            assert diff < 1e-6, f"Results should be deterministic, got diff={diff}"
            print("  ✅ Determinism test passed")
        
        print("\n" + "=" * 80)
        print("✅ ALL CORRECTNESS TESTS PASSED")
        print("=" * 80)
        return True
        
    except Exception as e:
        print(f"\n❌ CORRECTNESS TEST FAILED: {str(e)}")
        import traceback
        traceback.print_exc()
        return False


def main():
    """Main test runner."""
    
    # Check if test data exists
    test_data_path = 'webui/score_v1.csv'
    
    if not os.path.exists(test_data_path):
        print(f"❌ Test data not found: {test_data_path}")
        print("Please ensure the test data file exists.")
        return
    
    print("Loading test data...")
    df_proba = pd.read_csv(
        test_data_path,
        usecols=['time', 'ticker', 'close', 'price', 'score', 'volume', 'volume_p50_1m']
    )
    print(f"✓ Loaded {len(df_proba)} rows")
    
    # Create small sample for correctness tests
    df_proba_small = df_proba.sample(min(10000, len(df_proba)), random_state=42)
    
    # Run correctness tests first
    if not test_correctness(df_proba):
        print("\n⚠️  Correctness tests failed. Skipping performance tests.")
        return
    
    # Run performance tests
    perf_results = test_simulation_performance(
        df_proba, 
        iterate_values=[10, 50],  # Adjust based on your needs
        num_proc=10
    )
    
    # Save results
    output_file = 'performance_test_results.json'
    with open(output_file, 'w') as f:
        json.dump(perf_results, f, indent=4)
    
    print(f"\n📝 Results saved to: {output_file}")
    print("\n✅ All tests completed!")


if __name__ == "__main__":
    main()

