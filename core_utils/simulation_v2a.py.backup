import datetime
import json
import uuid
import numpy as np
import pandas as pd


# config = {
#     'initial_amount': 1e7,
#     'cutloss': 0.15,
#     'cutloss_duration': 30, # number of days not buying after cutloss
#     'ratio_nav': 0.9, # ratio between invest amount and nav (we will not buy more than ratio_nav * nav)
#     'ratio_deal': 0.1, # ratio between each deal amount and invest amount (we will not buy more than ratio_deal * ratio_nav * nav))
#     'ratio_deal_volume': 0.1, # ratio between each deal and daily volume (we will not buy more than ratio_deal_volume * daily_amount )
#     'review_frequency': 'monthly',
#     'fee_buy_rate': 0.001,
#     'fee_sell_rate': 0.002,
#     'score_sell': 0.5,
#     'score_buy': 1,
#     'gamma': 1,
#     'min_ratio_deal_nav': 0.01, # for buy or partial sell
# }

# portfolio:
# * data:
# - cash: float
# - fee_rate: float
# - holdings: ticker, holding_amount, adj_price, init_score, current_score, investment_amount
# - transaction: ymd, ticker, action, buy_amount, sell_amount, adj_price, fee
# * method
# - init(initial_amount)
# - update_ticker(data): update holdings at end of day based on new data(ymd, ticker, adj_price, score)
# - update_holding(data): update holding in the morning based on order plan (ymd, action, ticker, adj_price, amount)


# simulation:
# * data:
# - portfolio: current portfolio
# - config: config of the simulation
# - invest_ratio: float # usually 1. but can be lower based on market condition
# - daily_stats: ymd, nav, cash, list_holding, list_buy, list_sell
# * method
# - init(config): initialize the simulation
# - reset(config=None): reset the simulation
# - set_invest_ratio(invest_ratio): set the invest ratio
# - run(data): main function to run the simulation

def move_forward_date(ymd, days, date_format='%Y-%m-%d'):
    return (pd.to_datetime(ymd, format=date_format) + pd.Timedelta(days=days)).strftime(date_format)


class Portfolio:
    def __init__(self, initial_amount, fee_buy_rate, fee_sell_rate):
        self.cash = initial_amount
        self.fee_buy_rate = fee_buy_rate
        self.fee_sell_rate = fee_sell_rate
        self.holdings = {}  # dict ['ticker'] of dict { 'holding_amount', 'adj_price', 'init_score', 'current_score', 'investment_amount'}
        self.transactions = []  # list of dict {'ymd', 'ticker', 'action', 'adj_price', 'transaction_amount', 'fee', 'investment_amount'}
        self.log = []  # list of dict {'ymd', 'cash', 'nav', 'num_holdings', 'num_transactions'}

    def update_score_close_price(self, data):
        ''' update holdings at end of day based on new data index by ticker, columns (close_price, score) '''
        for ticker in self.holdings.keys():
            if ticker in data.index:
                try:
                    self.holdings[ticker] = self._update_one_holding(self.holdings[ticker],
                                                                     data.loc[ticker]['close_price'],
                                                                     data.loc[ticker]['score'])
                except:
                    print(
                        f"""Portfolio.update_score_close_price: Warning data not found for {ticker} on {data.index.name}""")
        # sort holdings by current score from low to high
        self.holdings = dict(sorted(self.holdings.items(), key=lambda x: x[1]['current_score'], reverse=False))

    def update_log(self, ymd):
        self.log.append({'ymd': ymd, 'cash': self.cash,
                         'nav': self.cash + sum(holding['holding_amount'] for holding in self.holdings.values()),
                         'num_holdings': len(self.holdings), 'num_transactions': len(self.transactions)})

    def _update_one_holding(self, holding, new_price, score=None):
        if new_price != holding['adj_price']:
            holding['holding_amount'] = holding['holding_amount'] * new_price / holding['adj_price']
            holding['adj_price'] = new_price
        if score is not None:
            holding['current_score'] = score
        return holding

    def calculate_delta_price(self, v_sell, v_prev, k=0.1, p_latest=1):
        if np.isnan(v_sell) or np.isnan(v_prev) or v_prev == 0 or v_sell == 0:
            return 0.1
        return min(0.1, p_latest * (1 - np.exp(-k * (v_sell / v_prev))))

    def execute_plan(self, ymd, plans):
        ''' update holding in the morning based on order plan - list of dict (action, ticker, amount, score, adj_price) 
        the holding might not be updated with latest price
        the plans should be updated with latest price
        '''
        if plans is None:
            return
        cnt = 0

        # step-by-step SELL:
        # - compute transaction amount and fee
        # - update holding list
        # - update cash
        # - append transaction
        for plan in plans:
            ticker = plan['ticker']
            if plan['action'] != 'sell':
                continue
            # if amount is larger than holding, sell all
            if ticker not in self.holdings:
                print(f"""execute_plan: Warning ticker not in holdings: {ticker}""")
                continue
            # update adj_price, amount of the holding
            self.holdings[ticker] = self._update_one_holding(self.holdings[ticker], plan['adj_price'])

            transaction_amount = min(plan['amount'], self.holdings[ticker]['holding_amount'])
            fee = transaction_amount * self.fee_sell_rate
            # investment amount corresponds to the amount of the transaction
            original_buy_amount = self.holdings[ticker]['investment_amount'] * transaction_amount / \
                                  self.holdings[ticker]['holding_amount']
            self.cash += (transaction_amount - fee)

            # use holding_id from holdings
            holding_id = self.holdings[ticker]['holding_id']

            # append transaction
            self.transactions += [{'ymd': ymd,
                                   'ticker': ticker,
                                   'holding_id': holding_id,
                                   'action': 'sell',
                                   'adj_price': plan['adj_price'],
                                   'buy_amount': original_buy_amount,
                                   'sell_amount': transaction_amount,
                                   'fee': fee}]
            cnt += 1

            self.holdings[ticker]['holding_amount'] -= transaction_amount
            self.holdings[ticker]['investment_amount'] -= original_buy_amount
            if self.holdings[ticker]['holding_amount'] <= 10000.0:
                del self.holdings[ticker]

        # step-by-step BUY:
        # - compute transaction amount and fee
        # - update holding
        # - update cash
        # - append transaction
        for plan in plans:
            if plan['action'] != 'buy':
                continue
            # print(f"""Plan amount: {plan['amount']}
            # Cash: {self.cash}
            # Fee buy rate: {self.fee_buy_rate}
            # """)
            transaction_amount = min(plan['amount'], self.cash * (1.0 - self.fee_buy_rate))
            fee = transaction_amount * self.fee_buy_rate

            if plan['ticker'] in self.holdings:
                self.holdings[plan['ticker']]['holding_amount'] += transaction_amount
                self.holdings[plan['ticker']]['investment_amount'] += transaction_amount
                holding_id = self.holdings[plan['ticker']]['holding_id']
            else:
                # new holding_id when first buy
                holding_id = str(uuid.uuid4())
                self.holdings[plan['ticker']] = {'holding_id': holding_id,
                                                 'holding_amount': transaction_amount,
                                                 'adj_price': plan['adj_price'],
                                                 'init_score': plan['score'],
                                                 'current_score': plan['score'],
                                                 'investment_amount': transaction_amount}
            self.cash -= (transaction_amount + fee)
            self.transactions += [{'ymd': ymd,
                                   'ticker': plan['ticker'],
                                   'holding_id': holding_id,
                                   'action': 'buy',
                                   'adj_price': plan['adj_price'],
                                   'buy_amount': transaction_amount,
                                   'sell_amount': 0,
                                   'fee': fee}]
            cnt += 1
        if cnt != len(plans):
            print(f"""execute_plan: Warning cnt != len(plans) cnt: {cnt} len(plans): {len(plans)}""")


class Simulation:
    def __init__(self, config):
        self.portfolio = Portfolio(initial_amount=config['initial_amount'], fee_buy_rate=config['fee_buy_rate'],
                                   fee_sell_rate=config['fee_sell_rate'])
        self.config = config
        self.invest_ratio = 1
        self.cutloss_dict = {}

    def set_invest_ratio(self, invest_ratio):
        if (invest_ratio > 1) or (invest_ratio < 0):
            raise ValueError('invest_ratio must be between 0 and 1')
        self.invest_ratio = invest_ratio
        return self

    def reset(self, config=None):
        if config is None:
            config = self.config
        self.portfolio = Portfolio(initial_amount=config['initial_amount'], fee_buy_rate=config['fee_buy_rate'],
                                   fee_sell_rate=config['fee_sell_rate'])
        self.config = config
        self.cutloss_dict = {}

    def run(self, data):
        ''' Run the simulation
        Input: dataframe with columns: ymd, ticker, open_price, close_price, score, daily_amount
        Output: None
        The simulation will be run for each day in the input data.
        '''
        pdx = data.set_index('ymd')
        ymd_list = sorted(pdx.index.unique())
        plans = None
        for ymd in ymd_list:
            # print(ymd)
            #                     =data.set_index('ymd').loc['2018-10-22'].reset_index(drop=True).head(2)
            xx = pdx.loc[ymd].reset_index(drop=True)
            data_ymd = xx.set_index('ticker').sort_values('score', ascending=False)
            plans = self.run_day(ymd, data_ymd, plans)
            self.portfolio.update_log(ymd)

    def run_day(self, ymd, data, plans=None):
        ''' Run the simulation for a single day
        Input:
        - data: dataframe (ticker, open_price, close_price, score, daily_amount)
        - plans: list of dict (action, ticker, amount)
        Output:
        - new plans: list of dict (action, ticker, amount, adj_price)
        Process:
        - update price and score of holding tickers
        - execute plans
        - prepare order plan for the next day
        '''
        # update plan adj_price with data.
        # print(f"""run {ymd}
        # plans: {plans}""")
        if plans is not None:
            for plan in plans:
                try:
                    # for sell, update sell amount with open price
                    if plan['action'] == 'sell':
                        plan['amount'] = plan['amount'] * data.loc[plan['ticker'], 'open_price'] / plan['adj_price']
                        plan['daily_amount'] = data.loc[plan['ticker'], 'daily_amount']
                    # for both, update adj_price with open price
                    plan['adj_price'] = data.loc[plan['ticker'], 'open_price']
                except:
                    if self.config.get('verbose', False):
                        print(f"""run_day: Warning data not found for {plan['ticker']} on {ymd}""")
        # execute plan
        self.portfolio.execute_plan(ymd,
                                    plans)  # sell and buy. Note: we should use the updated adj_price in plans (since holding does not necessarily has the adjusted price for all tickers)=
        # update ticker with score and close price
        self.portfolio.update_score_close_price(data)
        # prepare order plan for the next day
        new_plans = self.prepare_order_plan(ymd, data)
        if len(new_plans) > 0:
            if self.config.get('verbose', False):
                print(
                    f"""{ymd} nav(B)={(self.portfolio.cash + sum(holding['holding_amount'] for holding in self.portfolio.holdings.values())) / 1e+9:.3f} cash(B): {self.portfolio.cash / 1e+9:.3f} holdings: {len(self.portfolio.holdings)}
                new_plans: {new_plans} holdings={len(self.portfolio.holdings)}""")
            # display(pd.DataFrame(self.portfolio.holdings))
        return new_plans

    def _find_potential_swaps(self, target_ticker, target_score, expected_amount, cash, min_buy_amount, invest):
        swaps = {}
        acc_amount = cash
        for ticker, e in invest.items():
            if ticker == target_ticker:
                continue
            if e['score'] <= target_score:
                if acc_amount + e['amount'] > expected_amount:
                    swaps[ticker] = -(expected_amount - acc_amount)  # negative amount means sell
                    acc_amount = expected_amount
                    break
                else:
                    swaps[ticker] = -e['amount']
                    acc_amount += e['amount']
            else:
                break
        if acc_amount >= min_buy_amount:
            swaps[target_ticker] = acc_amount  # positive amount means buy
        else:
            swaps = {}
        return swaps

    def prepare_order_plan(self, ymd, data_input):
        ''' Prepare order plan for the next day
        Input:
            - ymd: ymd
            - data (ticker, open_price, close_price, score, daily_amount), sorted by score from high to low
            - self.portfolio: current portfolio with holdings (ticker | holding_amount, adj_price, init_score, current_score, investment_amount) and cash

        Output: list of dict (action, ticker, amount)
        Process:
        Optimization problem:
        maximize sum_i invest_i * score_i
               - |invest_i - holding_i|*0.5*gamma
        st. invest_i = 0 if score_i < score_sell
            invest_i-holding_i = 0 if score_i < score_buy
            invest_i < max_amount_i
            if invest_i-holding_i > 0:
                (invest_i-holding_i >= min_buy_amount)
            sum(invest_i) <= nav * invest_ratio
            invest_i >= 0
        Input/Argument:
        - holding_i      : current holding amount of ticker i=1..N
        - score_i        : score of ticker i=1..N
        - max_amount_i   : max amount of ticker i=1..N (capped by daily_amount and invest_amount and ratio_ticker)
        - cash           : available cash
        - config.min_ratio_deal_nav : min deal ratio to nav
#        -> min_buy_amount  = min_ratio_deal_nav* nav # min buying amount of a transaction (for complete buy or partial sell)
        - config.invest_ratio   : invest ratio
        - config.score_sell     : score to sell
        - config.score_buy      : score to buy
        - config.gamma          : default = 1 (score diff)

        Note:
        - if we sell a ticker, will we sell all?
        -- Situation: holding j with 0.2 nav , potential i with 0.01 nav
        -- Will we sell all j, to buy i?
        - buy and sell at the same time?
        - score is still high but hit cut_loss
        -> update cutloss_dict (remove expired tickers and add new cutloss)
        -> override score of ticker with cutloss_dict



        Step-by-step: compute optimal invest amount for each ticker
        - init: invest_i = holding_i
        - sell full tickers with current_score < score_sell (and update cash)
        - sell partial tickers if holding amount > expected amount (nav) + min_ratio_deal_nav * nav * 0.5 # we allow the ticker to gain 0.5 slot. If we sell, then delta = sell holding - expected amount
        - find change to buy good ticker:
        - loop over top ticker i with score larger than lowest holding
        -- compute expected amount, and delta = expected - holding
        -- if delta < min_buy_amount, then skip
        -- if (cash >= delta), then buy the difference, and update cash
        -- if (cash <= delta), then find potential swaps to make
        -- If total swap + cash >= min_buy_amount, then make sell until cash>delta or no more swap, and buy ticker i with amount min(delta,cash)




        def find_potential_swap(score_i, cash, min_buy_amount, gamma, holdings)
        - loop over holding j from lowest to highest
        -- if (score_i - score_j > gamma), then mark j as potential swap, if total_swap + cash >= min_buy_amount, then break
        -- else break
        - output: list of potential swaps (ticker_j, amount_j)

        '''
        data = data_input.copy()
        cash = self.portfolio.cash
        nav = self.portfolio.cash + sum(holding['holding_amount'] for holding in self.portfolio.holdings.values())
        min_buy_amount = self.config['min_ratio_deal_nav'] * nav
        slot_amount = nav * self.config['ratio_nav'] * self.config['ratio_deal']
        gamma = self.config['gamma']
        score_sell = self.config['score_sell']
        score_buy = self.config['score_buy']
        ratio_deal_volume = self.config['ratio_deal_volume']
        cutloss = self.config['cutloss']
        cutloss_duration = self.config['cutloss_duration']

        # update cutloss_dict
        for ticker in self.portfolio.holdings.keys():
            if self.portfolio.holdings[ticker]['holding_amount'] / self.portfolio.holdings[ticker][
                'investment_amount'] < (1 - cutloss):
                if self.config.get('verbose', False):
                    print(
                        f"""{ymd} add cutloss for {ticker} {self.portfolio.holdings[ticker]['holding_amount'] / self.portfolio.holdings[ticker]['investment_amount']}""")
                self.cutloss_dict[ticker] = move_forward_date(ymd, cutloss_duration)

        to_del = []
        for ticker in self.cutloss_dict.keys():
            if ymd > self.cutloss_dict[ticker]:
                to_del.append(ticker)
        for ticker in to_del:
            del self.cutloss_dict[ticker]

        data.loc[data.index.isin(self.cutloss_dict.keys()), 'score'] = score_sell - 10.

        invest = {ticker: {'score': holding['current_score'] if ticker not in self.cutloss_dict else score_sell - 10.,
                           'amount': holding['holding_amount']} for ticker, holding in self.portfolio.holdings.items()}

        # sell full tickers with current_score < score_sell
        # sell partial tickers if holding amount > expected amount + 0.5 slot
        for ticker, e in invest.items():
            if e['score'] <= score_sell:
                cash += e['amount']
                invest[ticker]['amount'] = 0
            else:
                expected_amount = slot_amount * self.score_to_ratio_nav(e['score'], score_buy, gamma)
                if e['amount'] > expected_amount + slot_amount * 0.5:
                    invest[ticker] = {'score': e['score'], 'amount': expected_amount}
                    cash += (e['amount'] - expected_amount)

        # buy tickers with cash then swaps score > lowest score + gamma
        for ticker in data.index:
            if data.loc[ticker]['score'] < score_buy:
                break  # the ticker is not good enough to buy
            expected_amount = min(slot_amount * self.score_to_ratio_nav(data.loc[ticker]['score'], score_buy, gamma),
                                  data.loc[ticker]['daily_amount'] * ratio_deal_volume)
            current_amount = 0 if ticker not in invest else invest[ticker]['amount']
            delta = expected_amount - current_amount

            if delta < min_buy_amount:
                continue  #

            if cash >= delta:
                invest[ticker] = {'score': data.loc[ticker]['score'], 'amount': expected_amount}
                cash -= delta
                continue

            # cash is not enough to buy the expected amount, then buy partial amount and find swaps
            swaps = self._find_potential_swaps(ticker, data.loc[ticker]['score'] - gamma, delta, cash, min_buy_amount,
                                               invest)

            # update invest
            for tt in swaps.keys():
                if tt in invest:
                    invest[tt]['amount'] = float(int(invest[tt]['amount'] + swaps[tt]))
                    cash -= swaps[tt]
                else:
                    invest[tt] = {'score': data.loc[tt]['score'], 'amount': swaps[tt]}
                    cash -= swaps[tt]
        plans = []
        for ticker in invest.keys():
            if ticker in self.portfolio.holdings:
                prev_amount = self.portfolio.holdings[ticker]['holding_amount']
            else:
                prev_amount = 0
            delta = invest[ticker]['amount'] - prev_amount
            if int(delta) != 0:
                if ticker in data.index:
                    close_price = data.loc[ticker]['close_price']
                else:
                    close_price = self.portfolio.holdings[ticker][
                        'adj_price']  # this only happens when data is incomplete and we what to sell the ticker
                if delta > 0:
                    plans.append(
                        {'action': 'buy', 'ticker': ticker, 'score': invest[ticker]['score'], 'adj_price': close_price,
                         'amount': delta})
                else:
                    plans.append(
                        {'action': 'sell', 'ticker': ticker, 'score': invest[ticker]['score'], 'adj_price': close_price,
                         'amount': -delta})
        return plans

    def get_stats(self):
        return {
            'cash': self.portfolio.cash,
            'nav': self.portfolio.cash + sum(holding['holding_amount'] for holding in self.portfolio.holdings.values()),
            'num_transactions': len(self.portfolio.transactions),
            'num_holdings': len(self.portfolio.holdings),
        }

    def score_to_ratio_nav(self, score, score_buy, gamma):
        if score >= score_buy + gamma * 2:
            return 3
        elif score >= score_buy + gamma:
            return 2
        elif score >= score_buy:
            return 1
        else:
            return 0


class WrapperSimulation(Simulation):
    def __init__(self, config):
        super().__init__(config)
        self.config = config

    def calculate_delta_price(self, v_sell, v_prev, k=0.1, p_latest=1):
        if np.isnan(v_sell) or np.isnan(v_prev) or v_prev == 0 or v_sell == 0:
            return 0.1
        return min(0.1, p_latest * (1 - np.exp(-k * (v_sell / v_prev))))

    # def score_to_ratio_nav(self, score, score_buy, gamma):
    #     if score >= score_buy + gamma * 2:
    #         return 3
    #     elif score >= score_buy + gamma:
    #         return 2
    #     elif score >= score_buy - gamma:
    #         return 1
    #     else:
    #         return 0

    def _is_review_day(self, ymd, review_frequency):
        if review_frequency == 'daily':
            return True
        elif review_frequency == 'weekly':
            return pd.to_datetime(ymd).weekday == 4  # Friday
        elif review_frequency == 'monthly':
            ymd_dt = pd.to_datetime(ymd)
            next_day = ymd_dt + pd.Timedelta(days=1)
            return next_day.month != ymd_dt.month  # Last day of month
        elif review_frequency == 'quarterly':
            ymd_dt = pd.to_datetime(ymd)
            next_day = ymd_dt + pd.Timedelta(days=1)
            return (next_day.month - 1) // 3 != (ymd_dt.month - 1) // 3  # Last day of quarter
        return False

    def prepare_order_plan(self, ymd, data_input):
        data = data_input.copy()
        cash = self.portfolio.cash
        nav = self.portfolio.cash + sum(holding['holding_amount'] for holding in self.portfolio.holdings.values())
        min_buy_amount = self.config['min_ratio_deal_nav'] * nav
        slot_amount = nav * self.config['ratio_nav'] * self.config['ratio_deal']
        gamma = self.config['gamma']
        score_sell = self.config['score_sell']
        score_buy = self.config['score_buy']
        ratio_deal_volume = self.config['ratio_deal_volume']
        cutloss = self.config['cutloss']
        cutloss_duration = self.config['cutloss_duration']
        review_frequency = self.config['review_frequency']
        is_review_day = self._is_review_day(ymd, review_frequency)

        # update cutloss_dict
        for ticker in self.portfolio.holdings.keys():
            if self.portfolio.holdings[ticker]['holding_amount'] / self.portfolio.holdings[ticker][
                'investment_amount'] < (1 - cutloss):
                if self.config.get('verbose', False):
                    print(
                        f"""{ymd} add cutloss for {ticker} {self.portfolio.holdings[ticker]['holding_amount'] / self.portfolio.holdings[ticker]['investment_amount']}""")
                self.cutloss_dict[ticker] = move_forward_date(ymd, cutloss_duration)

        to_del = []
        for ticker in self.cutloss_dict.keys():
            if ymd > self.cutloss_dict[ticker]:
                to_del.append(ticker)
        for ticker in to_del:
            del self.cutloss_dict[ticker]

        data.loc[data.index.isin(self.cutloss_dict.keys()), 'score'] = score_sell - 10.

        invest = {
            ticker: {'score': holding['current_score'] if ticker not in self.cutloss_dict else score_sell - 10.,
                     'amount': holding['holding_amount']} for ticker, holding in self.portfolio.holdings.items()}

        # sell full tickers with current_score < score_sell
        # sell partial tickers if holding amount > expected amount + 0.5 slot
        for ticker, e in invest.items():
            if e['score'] <= score_sell:
                cash += e['amount']
                invest[ticker]['amount'] = 0
            else:
                if is_review_day:
                    expected_amount = slot_amount * self.score_to_ratio_nav(e['score'], score_buy, gamma)
                    if e['amount'] > expected_amount + slot_amount * 0.5:
                        invest[ticker] = {'score': e['score'], 'amount': expected_amount}
                        cash += (e['amount'] - expected_amount)

        # buy tickers with cash then swaps score > lowest score + gamma
        for ticker in data.index:
            if data.loc[ticker]['score'] < score_buy:
                break  # the ticker is not good enough to buy
            expected_amount = min(
                slot_amount * self.score_to_ratio_nav(data.loc[ticker]['score'], score_buy, gamma),
                data.loc[ticker]['daily_amount'] * ratio_deal_volume)
            current_amount = 0 if ticker not in invest else invest[ticker]['amount']
            delta = expected_amount - current_amount

            if delta < min_buy_amount:
                continue  #

            if cash >= delta:
                invest[ticker] = {'score': data.loc[ticker]['score'], 'amount': expected_amount}
                cash -= delta
                continue

            if is_review_day:
                # cash is not enough to buy the expected amount, then buy partial amount and find swaps
                swaps = self._find_potential_swaps(ticker, data.loc[ticker]['score'] - gamma, delta, cash,
                                                   min_buy_amount,
                                                   invest)

                # update invest
                for tt in swaps.keys():
                    if tt in invest:
                        invest[tt]['amount'] = float(int(invest[tt]['amount'] + swaps[tt]))
                        cash -= swaps[tt]
                    else:
                        invest[tt] = {'score': data.loc[tt]['score'], 'amount': swaps[tt]}
                        cash -= swaps[tt]
        plans = []
        for ticker in invest.keys():
            if ticker in self.portfolio.holdings:
                prev_amount = self.portfolio.holdings[ticker]['holding_amount']
            else:
                prev_amount = 0
            delta = invest[ticker]['amount'] - prev_amount
            if int(delta) != 0:
                if ticker in data.index:
                    close_price = data.loc[ticker]['close_price']
                else:
                    close_price = self.portfolio.holdings[ticker][
                        'adj_price']  # this only happens when data is incomplete and we what to sell the ticker
                if delta > 0:
                    plans.append(
                        {'action': 'buy', 'ticker': ticker, 'score': invest[ticker]['score'],
                         'adj_price': close_price,
                         'amount': delta})
                else:
                    plans.append(
                        {'action': 'sell', 'ticker': ticker, 'score': invest[ticker]['score'],
                         'adj_price': close_price,
                         'amount': -delta})
        return plans

    # ====== Stat =======
    def stat_logs(self, logs_df):
        def drawdown_series(df: pd.DataFrame) -> pd.DataFrame:
            """
            Compute the drawdown time series from NAV.

            Intuition:
            - Peak NAV to date (running max) tracks the highest water mark.
            - Drawdown at time t = NAV(t) / PeakToDate(t) - 1  (≤ 0)
            - When NAV hits a new high, drawdown resets to 0.

            Parameters
            ----------
            df : pd.DataFrame
                Must contain column 'nav' and be indexed by date.

            Returns
            -------
            pd.DataFrame
                Columns:
                - 'nav'        : current NAV
                - 'peak_nav'   : running max of NAV
                - 'drawdown'   : relative drop from the running max (≤ 0)
            """
            s = df['nav'].astype(float)
            peak = s.cummax()  # running peak (highest so far)
            dd = (s / peak) - 1.0  # negative or zero
            return pd.DataFrame({'nav': s, 'peak_nav': peak, 'drawdown': dd}, index=df.index)

        def preprocess(df):
            out = df.copy()

            if 'ymd' in out.columns:
                out['date'] = pd.to_datetime(out['ymd'])
            elif 'date' in out.columns:
                out['date'] = pd.to_datetime(out['date'])

            out = out.sort_values('date').reset_index(drop=True)
            # out.set_index('date', inplace=True)

            # NAV & return
            if 'nav' in out.columns:
                out['daily_return'] = out['nav'].pct_change()

            # cash ratio
            if 'cash' in out.columns:
                out['cash_ratio'] = out['cash'] / out['nav']
                out['utilization'] = 1 - out['cash_ratio']

            return out

        def perf_stats(df: pd.DataFrame, rf: float = 0.09, periods_per_year: int = 240):
            """
           Compute core performance & risk statistics.

           Metrics:
           - total_return : (NAV_end / NAV_start - 1)
           - CAGR         : geometric annualized growth rate over the full period
           - ann_return   : mean daily return * periods_per_year (simple annualization)
           - ann_vol      : std(daily_return) * sqrt(periods_per_year)
           - Sharpe       : (ann_return - rf) / ann_vol
           - Sortino      : (ann_return - rf) / ann_downside,
                            where ann_downside uses std of negative daily returns only
           - max_drawdown : min(drawdown series)
           - Calmar       : ann_return / |max_drawdown|
           - period_days  : calendar length in days
           - obs          : number of rows

           Notes
           -----
           - `CAGR` uses geometric compounding across the actual time span,
             which is robust to irregular sample sizes.
           - `ann_return` is a linear annualization of mean daily returns;
             it’s convenient for ratios (Sharpe/Sortino), but not equal to CAGR.

           Parameters
           ----------
           df : pd.DataFrame
               Must include 'nav' and 'daily_return' and be date-indexed.
           rf : float, optional
               Annual risk-free rate (in decimal), default 0.0.
           periods_per_year : int, optional
               Trading periods per year, default 252 (daily bars for equities).

           Returns
           -------
           Key performance statistics as described above.

           Raises
           ------
           ValueError
           If there are no valid daily returns.
           """

            r = df['daily_return'].dropna()
            if len(r) == 0:
                raise ValueError("Not enough data to compute returns.")

            # Total return and time span
            total_return = df['nav'].iloc[-1] / df['nav'].iloc[0] - 1.0
            n_days = (df['date'].max() - df['date'].min()).days
            n_years = max(n_days, 1) / 365.25  # avoid division by zero

            # si_return
            # CAGR via geometric compounding across the full period
            cagr = (1 + total_return) ** (1 / n_years) - 1 if n_years > 0 else np.nan

            # Annualized return & volatility for ratio-based metrics
            mean_daily = r.mean()
            vol_daily = r.std(ddof=1)
            ann_return = mean_daily * periods_per_year
            ann_vol = vol_daily * np.sqrt(periods_per_year)
            sharpe = (ann_return - rf) / ann_vol if ann_vol != 0 else np.nan

            # Downside deviation: only consider negative daily returns
            downside = r[r < 0].std(ddof=1)
            ann_downside = downside * np.sqrt(periods_per_year) if pd.notna(downside) else np.nan
            sortino = (ann_return - rf) / ann_downside if (pd.notna(ann_downside) and ann_downside != 0) else np.nan

            # Drawdown-based risk
            dd = drawdown_series(df)['drawdown']
            max_dd = dd.min()
            calmar = ann_return / abs(max_dd) if max_dd != 0 else np.nan

            return {
                'total_return': total_return,
                'total_time': n_days,
                'utilization': np.nanmean(df['utilization']),
                'CAGR': cagr,
                'ann_return': ann_return,
                'ann_vol': ann_vol,
                'Sharpe': sharpe,
                'Sortino': sortino,
                'max_drawdown': max_dd,
                'Calmar': calmar,
                'period_days': n_days + 1,
                'obs': len(df)
            }

        logs_df = preprocess(logs_df)
        return perf_stats(logs_df)

    def stat_transactions(self, transactions_df):
        def preprocess(df):
            """
            Optimized preprocess with vectorized quantity estimation.

            Key optimization: Replace .apply() with vectorized operations.
            """
            out = df.copy()

            # 1) Date & action normalization
            if 'ymd' in out.columns:
                out['date'] = pd.to_datetime(out['ymd'])
            elif 'date' in out.columns:
                out['date'] = pd.to_datetime(out['date'])

            out['action'] = out['action'].str.lower().str.strip()

            # 2) Ensure money/price columns exist and are numeric
            for col in ['buy_amount', 'sell_amount', 'fee', 'adj_price']:
                if col not in out.columns:
                    out[col] = 0.0
                out[col] = pd.to_numeric(out[col], errors='coerce').fillna(0.0)

            # Clip to avoid negatives in these fields (simulation logs should be non-negative)
            for col in ['buy_amount', 'sell_amount', 'fee']:
                out[col] = out[col].clip(lower=0.0)


            # Determine which amount to use based on action
            is_buy = out['action'] == 'buy'

            # For buy actions: prefer buy_amount, fallback to sell_amount
            # For sell actions: prefer sell_amount, fallback to buy_amount
            amt = np.where(
                is_buy,
                np.where(out['buy_amount'] > 0, out['buy_amount'], out['sell_amount']),
                np.where(out['sell_amount'] > 0, out['sell_amount'], out['buy_amount'])
            )

            # Calculate quantity: amt / price (handle division by zero)
            out['est_qty'] = np.where(
                out['adj_price'] > 0,
                amt / out['adj_price'],
                np.nan
            )

            out = out.sort_values(['date', 'holding_id']).reset_index(drop=True)
            out.set_index('date', inplace=True)

            return out

        def build_positions(df_tx: pd.DataFrame, qty_tol: float = 1e-8) -> pd.DataFrame:
            """
               Aggregate fills into position-level rows using `holding_id`.

               Lifecycle logic
               ---------------
               - Group rows by `holding_id` and sort by date.
               - Position open_date = first fill date; close_date = last fill date.
               - Buys add positive quantity; sells subtract quantity (best-effort via est_qty).

               PnL & pricing
               -------------
               - gross_pnl = sum(sell_amount) - sum(buy_amount)
               - net_pnl   = gross_pnl - sum(fee)
               - invested  = sum(buy_amount)  (capital deployed)
               - ret_net   = net_pnl / invested  (NaN if invested==0)
               - avg_buy_price  = sum(buy_amount)  / sum(buy_qty)  (if buy_qty>0)
               - avg_sell_price = sum(sell_amount) / sum(sell_qty) (if sell_qty>0)
               - ret_period = (close_price - open_price) / open_price

               Returns
               -------
               pd.DataFrame
                   One row per holding_id with:
                   - ticker, open_date, close_date, trades_count
                   - buy_notional, sell_notional, fee_total
                   - buy_qty, sell_qty, qty_net, status ('closed'|'open')
                   - gross_pnl, net_pnl, invested, ret_net, ret_period
                   - avg_buy_price, avg_sell_price
                   - holding_days (inclusive)
               """

            g = df_tx.groupby('holding_id', sort=False)

            rows = []
            for hid, grp in g:
                grp = grp.sort_index()
                ticker = grp['ticker'].iloc[0]
                open_date = grp.index.min()
                close_date = grp.index.max()
                open_quarter = grp.index.min().to_period('Q').strftime('%YQ%q')
                close_quarter = grp.index.max().to_period('Q').strftime('%YQ%q')

                open_price = grp['adj_price'].iloc[0]
                close_price = grp['adj_price'].iloc[-1]
                trades_count = len(grp)

                # Quantities by action (best-effort)
                buy_mask = grp['action'].eq('buy')
                sell_mask = grp['action'].eq('sell')

                buy_notional = grp.loc[buy_mask, 'buy_amount'].sum()
                sell_notional = grp.loc[sell_mask, 'sell_amount'].sum()
                fee_total = grp['fee'].sum()

                buy_qty = grp.loc[buy_mask, 'est_qty'].sum(min_count=1)
                sell_qty = grp.loc[sell_mask, 'est_qty'].sum(min_count=1)

                if pd.isna(buy_qty): buy_qty = 0.0
                if pd.isna(sell_qty): sell_qty = 0.0

                qty_net = buy_qty - sell_qty

                gross_pnl = sell_notional - buy_notional
                net_pnl = gross_pnl - fee_total

                invested = buy_notional if buy_notional > 0 else np.nan
                ret_net = (net_pnl / invested) if pd.notna(invested) and invested != 0 else np.nan

                avg_buy_price = (buy_notional / buy_qty) if buy_qty > 0 else np.nan
                avg_sell_price = (sell_notional / sell_qty) if sell_qty > 0 else np.nan

                holding_days = (close_date - open_date).days + 1

                ret_period = (close_price - open_price) / open_price

                rows.append({
                    'holding_id': hid,
                    'ticker': ticker,
                    'open_date': open_date,
                    'close_date': close_date,
                    'open_quarter': open_quarter,
                    'close_quarter': close_quarter,
                    'open_price': open_price,
                    'close_price': close_price,
                    'trades_count': trades_count,
                    'buy_notional': buy_notional,
                    'sell_notional': sell_notional,
                    'fee_total': fee_total,
                    'buy_qty': buy_qty,
                    'sell_qty': sell_qty,
                    'qty_net': qty_net,
                    'gross_pnl': gross_pnl,
                    'net_pnl': net_pnl,
                    'invested': invested,
                    'ret_net': ret_net,
                    'ret_period': ret_period,
                    'avg_buy_price': avg_buy_price,
                    'avg_sell_price': avg_sell_price,
                    'holding_days': holding_days
                })

            pos = pd.DataFrame(rows).sort_values(['open_date', 'holding_id']).reset_index(drop=True)
            # Helpful time fields
            pos['open_month'] = pos['open_date'].dt.to_period('M').astype(str)
            pos['close_month'] = pos['close_date'].dt.to_period('M').astype(str)
            return pos

        def trade_metrics(pos: pd.DataFrame, closed_only: bool = True):
            """
            Compute headline trade-performance metrics from position table.

            Metrics (closed-only by default)
            --------------------------------
            - n_trades, win_rate
            - avg_win, avg_loss
            - profit_factor = sum(win_pnl) / abs(sum(loss_pnl))
            - expectancy    = p(win)*avg_win - p(loss)*avg_loss
            - median_pnl, p25, p75, p90
            - holding_days_mean/median

            Returns
            -------
            pd.Series
            """
            df = pos.copy()
            # if closed_only:
            #     df = df[df['status'] == 'closed'].copy()

            pnl = df['net_pnl'].dropna()
            n = len(pnl)
            if n == 0:
                return pd.Series({
                    'n_trades': 0, 'win_rate': np.nan,
                    'avg_win': np.nan, 'avg_loss': np.nan,
                    'profit_factor': np.nan, 'expectancy': np.nan,
                    'median_pnl': np.nan, 'p25_pnl': np.nan, 'p75_pnl': np.nan, 'p90_pnl': np.nan,
                    'holding_days_mean': np.nan, 'holding_days_median': np.nan
                })

            wins = pnl[pnl > 0]
            losses = pnl[pnl < 0]

            p_win = len(wins) / n
            p_loss = len(losses) / n

            avg_win = wins.mean() if len(wins) > 0 else 0.0
            avg_loss = losses.mean() if len(losses) > 0 else 0.0  # negative

            profit_factor = (wins.sum() / abs(losses.sum())) if len(losses) > 0 else np.inf
            expectancy = p_win * avg_win + p_loss * avg_loss  # avg_loss is negative

            holding_days_mean = df['holding_days'].mean() if 'holding_days' in df.columns else np.nan
            holding_days_median = df['holding_days'].median() if 'holding_days' in df.columns else np.nan

            return {
                'n_trades': n,
                'win_rate': p_win,
                'avg_win': avg_win,
                'avg_loss': avg_loss,
                'profit_factor': profit_factor,
                'expectancy': expectancy,
                'median_pnl': pnl.median(),
                'p25_pnl': pnl.quantile(0.25),
                'p75_pnl': pnl.quantile(0.75),
                'p90_pnl': pnl.quantile(0.90),
                'holding_days_mean': holding_days_mean,
                'holding_days_median': holding_days_median
            }

        tx = preprocess(transactions_df)
        pos = build_positions(tx)
        tmetrics = trade_metrics(pos, closed_only=True)

        return {
            # 'tx': tx,
            'pos': pos,
            'tmetrics': tmetrics,
        }

    def stats(self):
        logs_df = pd.DataFrame(self.portfolio.log)
        transactions_df = pd.DataFrame(self.portfolio.transactions)

        # Complete all holdings
        hold_transactions = []
        now = datetime.datetime.now().strftime('%Y-%m-%d')
        for ticker, row in self.portfolio.holdings.items():
            original_buy_amount = row['investment_amount'] * row['holding_amount'] / \
                                  row['holding_amount']
            fee = row['holding_amount'] * self.portfolio.fee_sell_rate
            hold_transactions.append({
                'ymd': now,
                'ticker': ticker,
                'holding_id': row['holding_id'],
                'action': 'sell',
                'adj_price': row['adj_price'],
                'buy_amount': original_buy_amount,
                'sell_amount': row['holding_amount'],
                'fee': fee
            })

        transactions_df = pd.concat([transactions_df, pd.DataFrame(hold_transactions)], ignore_index=True)

        stats_log = self.stat_logs(logs_df)
        stats_tx = self.stat_transactions(transactions_df)

        # total_time = (logs_df['date'].max() - logs_df['date'].min()).days
        # initial_nav = logs_df.loc[logs_df.index[0], 'nav']
        # newest_nav = logs_df.loc[logs_df.index[-1], 'nav']

        return {
            'logs': logs_df,
            'transactions': transactions_df,
            'stats_log': stats_log,
            'stats_tx': stats_tx
            # 'return': ((newest_nav / initial_nav) ** (365 / total_time) - 1) * 100,
            # 'utilization': np.nanmean(logs_df['utilization']),
            # 'win_deal': 0,
            # 'win_quarter': 0,
            # 'uni_ticker': 0,
            # 'peak_number_deals': 0,
            # 'nav': self.portfolio.cash + sum(holding['holding_amount'] for holding in self.portfolio.holdings.values()),
            # 'num_transactions': len(self.portfolio.transactions),
            # 'num_holdings': len(self.portfolio.holdings),
        }


# Usage:
if False:
    # exec(open('simulation_v2b.py').read())
    def load_score(fname, format='%m/%d/%Y'):
        pdX = pd.read_csv(fname)
        pdX['time'] = pd.to_datetime(pdX['time'], format=format)
        pdX['ymd'] = pdX['time'].dt.strftime('%Y-%m-%d')

        ii = (pdX['close'] > 0) & (pdX['price'] > 0) & (pdX['volume_p50_1m'] > 0) & (
                pdX['score'].notnull() & (pdX['ymd'] >= "2014-01-01"))
        data = pdX[ii][['ymd', 'ticker', 'close', 'price', 'score', 'volume_p50_1m']].copy().rename(
            columns={'close': 'close_price'})
        data['open_price'] = data['close_price']
        data['daily_amount'] = data['volume_p50_1m'] * data['price']
        data.drop(columns=['volume_p50_1m', 'price'], inplace=True)

        return data


    config = {
        'initial_amount': 1e9,
        'cutloss': 0.2,
        'cutloss_duration': 15,  # number of days not buying after cutloss
        'ratio_nav': 1.0,  # ratio between invest amount and nav (we will not buy more than ratio_nav * nav)
        'ratio_deal': 0.1,
        # ratio between each deal amount and invest amount (we will not buy more than ratio_deal * ratio_nav * nav))
        'ratio_deal_volume': 0.1,
        # ratio between each deal and daily volume (we will not buy more than ratio_deal_volume * daily_volume * price_target)
        'review_frequency': 'weekly',
        'fee_buy_rate': 0.0015,
        'fee_sell_rate': 0.0015,
        'score_sell': 0.5,
        'score_buy': 1,
        'gamma': 1,
        'min_ratio_deal_nav': 0.01,  # for buy or partial sell
        'verbose': False,
    }

    data = load_score('score/score_v1.csv', format='%Y-%m-%d').query('ymd >= "2022-06-01"')
    sim = Simulation(config)
    sim.run(data)
    print('original')
    print(sim.get_stats())
    pd.DataFrame(sim.portfolio.log).set_index('ymd')['nav'].plot(figsize=(10, 5), grid=True)

    import matplotlib.pyplot as plt

    nav = pd.DataFrame(sim.portfolio.log)
    nav['date'] = pd.to_datetime(nav['ymd'], format='%Y-%m-%d')
    nav.set_index('date', inplace=True)
    fig, ax = plt.subplots(figsize=(20, 10))
    ax.plot(nav.index, nav['nav'], label='NAV')
    ax.set_ylim(300000000.0, 2000000000.0)  # fix trục y
    ax.set_xlabel('Date')
    ax.set_ylabel('NAV')
    ax.set_title('NAV Over Time')
    ax.legend()
    plt.show()

    config['review_frequency'] = 'daily'
    sim_v2 = WrapperSimulation(config)
    sim_v2.run(data)
    print('daily')
    print(sim_v2.get_stats())
    pd.DataFrame(sim_v2.portfolio.log).set_index('ymd')['nav'].plot(figsize=(10, 5), grid=True)

    nav = pd.DataFrame(sim_v2.portfolio.log)
    nav['date'] = pd.to_datetime(nav['ymd'], format='%Y-%m-%d')
    nav.set_index('date', inplace=True)
    fig, ax = plt.subplots(figsize=(20, 10))
    ax.plot(nav.index, nav['nav'], label='NAV')
    ax.set_ylim(300000000.0, 2000000000.0)  # fix trục y
    ax.set_xlabel('Date')
    ax.set_ylabel('NAV')
    ax.set_title('NAV Over Time')
    ax.legend()
    plt.show()

    config['review_frequency'] = 'weekly'
    sim_v2 = WrapperSimulation(config)
    sim_v2.run(data)
    print('weekly')
    print(sim_v2.get_stats())
    pd.DataFrame(sim_v2.portfolio.log).set_index('ymd')['nav'].plot(figsize=(10, 5), grid=True)
    nav = pd.DataFrame(sim_v2.portfolio.log)
    nav['date'] = pd.to_datetime(nav['ymd'], format='%Y-%m-%d')
    nav.set_index('date', inplace=True)
    fig, ax = plt.subplots(figsize=(20, 10))
    ax.plot(nav.index, nav['nav'], label='NAV')
    ax.set_ylim(300000000.0, 2000000000.0)  # fix trục y
    ax.set_xlabel('Date')
    ax.set_ylabel('NAV')
    ax.set_title('NAV Over Time')
    ax.legend()
    plt.show()

    result = sim_v2.stats()
    # file_path = "stat_logs.json"
    # with open(file_path, 'w') as f:
    #     json.dump(result['stats_log'], f, indent=4)
    # result['stats_tx']['pos'].to_csv('stat_transactions_pos.csv', index=False)
    # result['stats_tx']['tmetrics'].to_csv('stat_transactions_tmetrics.csv', index=False)

    config['review_frequency'] = 'monthly'
    sim_v2 = WrapperSimulation(config)
    sim_v2.run(data)
    print('monthly')
    print(sim_v2.get_stats())
    pd.DataFrame(sim_v2.portfolio.log).set_index('ymd')['nav'].plot(figsize=(10, 5), grid=True)

    nav = pd.DataFrame(sim_v2.portfolio.log)
    nav['date'] = pd.to_datetime(nav['ymd'], format='%Y-%m-%d')
    nav.set_index('date', inplace=True)
    fig, ax = plt.subplots(figsize=(20, 10))
    ax.plot(nav.index, nav['nav'], label='NAV')
    ax.set_ylim(300000000.0, 2000000000.0)  # fix trục y
    ax.set_xlabel('Date')
    ax.set_ylabel('NAV')
    ax.set_title('NAV Over Time')
    ax.legend()
    plt.show()

    config['review_frequency'] = 'quarterly'
    sim_v2 = WrapperSimulation(config)
    sim_v2.run(data)
    print('quarterly')
    print(sim_v2.get_stats())
    pd.DataFrame(sim_v2.portfolio.log).set_index('ymd')['nav'].plot(figsize=(10, 5), grid=True)

    import matplotlib.pyplot as plt

    nav = pd.DataFrame(sim_v2.portfolio.log)
    nav['date'] = pd.to_datetime(nav['ymd'], format='%Y-%m-%d')
    nav.set_index('date', inplace=True)
    fig, ax = plt.subplots(figsize=(20, 10))
    ax.plot(nav.index, nav['nav'], label='NAV')
    ax.set_ylim(300000000.0, 2000000000.0)  # fix trục y
    ax.set_xlabel('Date')
    ax.set_ylabel('NAV')
    ax.set_title('NAV Over Time')
    ax.legend()
    plt.show()
