import os
import re
import sys
import traceback

import pandas as pd

from core_utils.base_eval import BaseEval

current_dir = os.path.dirname(os.path.abspath(__file__))
current_dir = current_dir.replace("/core_utils", "")
os.chdir(current_dir)
sys.path.insert(0, current_dir)


######2
def proba_to_score_power(p, alpha=0.7, min_score=-1, max_score=3, eps=1e-12):
    """
    Nonlinear mapping: score = min_score + (max_score - min_score) * (p**alpha)
    - alpha < 1: nhấn mạnh positive tail
    - alpha > 1: nhấn mạnh negative tail
    """
    p = np.clip(np.asarray(p, dtype=float), eps, 1 - eps)
    return min_score + (max_score - min_score) * (p ** alpha)


def score_to_proba_power(score, alpha=0.7, min_score=-1, max_score=3):
    score = np.asarray(score, dtype=float)
    normed = (score - min_score) / (max_score - min_score)
    return np.clip(normed ** (1 / alpha), 0, 1)


# =========================
# 1) CALIBRATION
# =========================

import numpy as np


# ============ Utils ============
def _logit(x): return np.log(x) - np.log(1 - x)


def _sigmoid(x): return 1 / (1 + np.exp(-x))


def _default_knots():
    # Default mapping from [0, 20, 40, 60, 80, 100] percentiles → scores in [-1, 3]
    return np.array([-1.0, -0.4, 1.0, 1.8, 2.95, 3.0], dtype=float)


def derive_knots_from_lift(target_rates, base_rate):
    """
    Derive score knots automatically from lift values.
    - target_rates: list of target positive rates at different segments.
    - base_rate: overall average positive rate.

    Returns:
        s6: array(6,) - score knots corresponding to percentiles [0, 20, 40, 60, 80, 100]
    """
    tr = np.asarray(target_rates, float)
    lifts = tr / float(base_rate)

    def safe_gamma(num, den):
        # Avoid division by near-zero
        if abs(den) < 1e-12: return np.inf
        return num / den

    # Estimate a common "gamma" scaling to keep scores in [-1, 3] range
    gamma_low = safe_gamma(-2.0, lifts[0] - 1.0)
    gamma_high = safe_gamma(2.0, lifts[-1] - 1.0)
    gamma = min(gamma_low if gamma_low > 0 else np.inf,
                gamma_high if gamma_high > 0 else np.inf)
    if not np.isfinite(gamma): gamma = 2.0

    # Raw values mapped from lift through gamma
    raw5 = 1.0 + gamma * (lifts - 1.0)

    # Keep the middle knot (40–60%) as pivot = 1.0
    raw5[2] = 1.0  # pivot 40–60
    # Define 5 control points corresponding to [10, 30, 50, 70, 90] percentiles
    knots5_pct = np.array([10, 30, 50, 70, 90])

    # Target percentiles for interpolation
    pct6 = np.array([0, 20, 40, 60, 80, 100])

    # Interpolate to get 6 values across full range
    s6 = np.interp(pct6, knots5_pct, raw5, left=raw5[0], right=raw5[-1])

    # Clip to valid score range
    s6 = np.clip(s6, -1.0, 3.0)
    s6[2] = 1.0
    return s6


# ============ Optional building blocks ============
class Calibrator:
    """
    Probability calibration options:
    kind: 'auto' | 'iso' | 'platt' | 'identity' | None
      - auto: try isotonic first, then Platt scaling, fallback to identity.
    """

    def __init__(self, kind="auto"):
        self.kind = kind
        self.model_ = None
        self.ab_ = None  # For Platt scaling (a, b) parameters

    def fit(self, p_valid, y_valid):
        p = np.clip(np.asarray(p_valid, float), 1e-9, 1 - 1e-9)
        y = np.asarray(y_valid, float)

        # --- Try isotonic regression calibration ---
        if self.kind in ("iso", "auto"):
            try:
                from sklearn.isotonic import IsotonicRegression
                iso = IsotonicRegression(y_min=1e-9, y_max=1 - 1e-9, out_of_bounds='clip')
                iso.fit(p, y)
                self.kind = "iso"
                self.model_ = iso
                return self
            except Exception:
                pass

        # --- Try Platt scaling (logistic regression on logits) ---
        if self.kind in ("platt", "auto"):
            try:
                from scipy.optimize import minimize
                def nll(ab):
                    # Negative log-likelihood loss for binary logistic calibration
                    a, b = ab
                    q = _sigmoid(a * p + b)
                    eps = 1e-9
                    return -(y * np.log(q + eps) + (1 - y) * np.log(1 - q + eps)).mean()

                res = minimize(nll, x0=[5.0, -2.5], method="L-BFGS-B")
                self.kind = "platt"
                self.ab_ = tuple(res.x)
                return self
            except Exception:
                pass

        # --- Fallback: identity calibration (no change) ---
        self.kind = "identity"
        return self

    def transform(self, p):
        p = np.clip(np.asarray(p, float), 1e-9, 1 - 1e-9)
        if self.kind == "iso" and self.model_ is not None:
            q = self.model_.predict(p)
        elif self.kind == "platt" and self.ab_ is not None:
            a, b = self.ab_
            q = _sigmoid(a * p + b)
        else:
            q = p
        return np.clip(q, 1e-9, 1 - 1e-9)


class TemperatureScaler:
    """
    Single-parameter temperature scaling.
    mode: 'brier' | 'none'
      - 'brier': grid search using Brier score minimization
      - 'none': skip fitting (t=1.0)
    """

    def __init__(self, use_temperature=False, mode="brier", grid=(0.5, 3.0, 26)):
        self.use_temperature = use_temperature
        self.mode = mode
        self.grid = grid
        self.t_ = 1.0

    def fit(self, p_valid, y_valid=None):
        if not self.use_temperature:
            self.t_ = 1.0
            return self
        p = np.clip(np.asarray(p_valid, float), 1e-9, 1 - 1e-9)
        z = _logit(p)
        if self.mode == "brier" and y_valid is not None:
            y = np.asarray(y_valid, float)
            lo, hi, n = self.grid
            ts = np.linspace(lo, hi, int(n))
            best_brier, best_t = np.inf, 1.0
            # Search for t minimizing Brier score
            for t in ts:
                q = _sigmoid(t * z)
                brier = np.mean((q - y) ** 2)
                if brier < best_brier:
                    best_brier, best_t = brier, t
            self.t_ = best_t
        else:
            self.t_ = 1.0
        return self

    def transform(self, p):
        if not self.use_temperature:
            return np.asarray(p, float)
        p = np.clip(np.asarray(p, float), 1e-9, 1 - 1e-9)
        return _sigmoid(self.t_ * _logit(p))


# ============ Unified Mapper ============
class ScoreEngineConfig:
    """
    Centralized configuration for ScoreEngine.

    Parameters:
      - calibrate_kind: 'auto' | 'iso' | 'platt' | 'identity' | None
      - use_temperature: bool
      - temp_mode: 'brier' | 'none'
      - percentiles: list of 6 percentile cutoffs (default [0, 20, 40, 60, 80, 100])
      - score_knots: optional pre-defined score values at those percentiles
      - lift_target_rates + base_rate: if provided → auto-generate knots via lift
      - clip_range: lower & upper clipping bounds for scores
    """

    def __init__(
            self,
            calibrate_kind="auto",
            use_temperature=False,
            temp_mode="brier",
            percentiles=(0, 20, 40, 60, 80, 100),
            score_knots=None,
            lift_target_rates=None,
            base_rate=None,
            clip_range=(-1.0, 3.0),
            **extra_kwargs
    ):
        self.calibrate_kind = calibrate_kind
        self.use_temperature = use_temperature
        self.temp_mode = temp_mode
        self.percentiles = np.array(percentiles, float) / 100.0
        self.score_knots = _default_knots() if score_knots is None else np.asarray(score_knots, float)
        self.lift_target_rates = lift_target_rates
        self.base_rate = base_rate
        self.clip_range = clip_range


class ScoreEngine:
    """
    End-to-end scoring pipeline:
      raw_probs → (temperature scaling?) → (calibration?) →
      → percentile mapping (based on validation) → → interpolated score.

    Each stage can be toggled independently.
    Knots can be default, custom, or derived automatically from lift targets.
    """

    def __init__(self, config: ScoreEngineConfig = None):
        self.cfg = config if config is not None else ScoreEngineConfig()
        self.temp_ = TemperatureScaler(
            use_temperature=self.cfg.use_temperature,
            mode=self.cfg.temp_mode
        )
        self.cal_ = Calibrator(kind=self.cfg.calibrate_kind) if self.cfg.calibrate_kind else None
        self.percentiles_values_ = None
        self.score_knots_ = np.asarray(self.cfg.score_knots, float)

    # --- fitting ---
    def fit(self, p_valid_raw=None, y_valid=None):
        """
                Fit the scoring engine.
                - If p_valid_raw is provided → perform temperature scaling & calibration.
                - If p_valid_raw is None → skip fitting; use config defaults for mapping.
        """

        # Case 1: no validation data → skip calibration + temperature
        if p_valid_raw is None:
            # Use default knots and percentiles
            self.score_knots_ = np.asarray(self.cfg.score_knots, float)
            self.percentiles_values_ = np.array(self.cfg.percentiles, float)
            print("[ScoreEngine] No validation data. Using default percentiles & score knots.")
            return self

        # Case 2: normal fitting pipeline
        p_valid_raw = np.asarray(p_valid_raw, float)

        # (1) Fit temperature scaling (optional)
        self.temp_.fit(p_valid_raw, y_valid)
        p_t = self.temp_.transform(p_valid_raw)

        # (2) Fit calibration (optional)
        if self.cal_ is not None and self.cfg.calibrate_kind != "identity":
            if y_valid is None:
                # If no labels, skip true calibration (use identity)
                self.cal_ = Calibrator(kind="identity").fit(p_t, np.zeros_like(p_t))
            else:
                self.cal_.fit(p_t, y_valid)
            p_tc = self.cal_.transform(p_t)
        else:
            p_tc = p_t

        # (3) Define score knots
        if self.cfg.lift_target_rates is not None and self.cfg.base_rate is not None:
            # Derive from target lift rates if provided
            self.score_knots_ = derive_knots_from_lift(self.cfg.lift_target_rates, self.cfg.base_rate)
        else:
            self.score_knots_ = np.asarray(self.cfg.score_knots, float)

        # (4) Compute percentile values for interpolation mapping
        qs = np.quantile(p_tc, self.cfg.percentiles)

        # Ensure strictly increasing percentiles (avoid ties breaking np.interp)
        eps = 1e-12
        for i in range(1, len(qs)):
            if qs[i] <= qs[i - 1]:
                qs[i] = qs[i - 1] + eps
        self.percentiles_values_ = qs
        return self

    # --- inference ---
    def transform(self, p_raw, clip=True):
        assert self.percentiles_values_ is not None, "Must call fit() first."
        p_raw = np.asarray(p_raw, float)

        # Apply the same transformations as training
        p_t = self.temp_.transform(p_raw)
        p_tc = self.cal_.transform(p_t) if self.cal_ is not None else p_t

        # Interpolate score based on fitted percentiles
        s = np.interp(p_tc, self.percentiles_values_, self.score_knots_)

        # Clip scores to desired range
        if clip:
            lo, hi = self.cfg.clip_range
            s = np.clip(s, lo, hi)
        return s

    # --- utilities ---

    def get_state(self):
        """Return fitted model state for reproducibility or debugging."""
        return {
            "config": {
                "calibrate_kind": self.cfg.calibrate_kind,
                "use_temperature": self.cfg.use_temperature,
                "temp_mode": self.cfg.temp_mode,
                "percentiles": (self.cfg.percentiles * 100).tolist(),
                "clip_range": self.cfg.clip_range,
            },
            "score_knots": self.score_knots_.tolist(),
            "percentiles_values": self.percentiles_values_.tolist(),
            "temperature": getattr(self.temp_, "t_", 1.0),
            "calibrator_kind": getattr(self.cal_, "kind", None),
            "calibrator_ab": getattr(self.cal_, "ab_", None),
        }


# ============ Market Evaluation ============

class MarketEvaluation(BaseEval):
    def __init__(self, cache_service=None):
        data_frame, d_filters = self._load_data()

        super().__init__(stock='VNINDEX', data_frame=data_frame, dict_filter=d_filters,
                         cache_service=cache_service)

        self.percentiles = self._init_percentiles()
        self.overheat_df = self._overheat()
        self.masks = []

    @staticmethod
    def _load_data(fpath='ticker_v1a'):
        df = pd.read_csv(f'{fpath}/VNINDEX.csv')
        d_filters = {
            "~BearDvgVNI1~": "(time>='2014-01-01') & (time<='2026-01-01') & (ticker=='VNINDEX') & (D_RSI_Max1W/D_RSI > 1.044)  & (D_RSI_Max3M > 0.74) & (D_RSI_Max1W < 0.72) & (D_RSI_Max1W>0.61) & (D_RSI_Max1W_Close/D_RSI_Max3M_Close > 1.028) & (D_RSI_Max3M_MACD/D_RSI_Max1W_MACD>1.11) & (D_MACDdiff < 0)  & ( Close/D_RSI_Max3M_Close > 0.96) & (D_RSI_MinT3 > 0.43) & (D_CMF < 0.13)",
            "~BearDvgVNI2~": "(time>='2014-01-01') & (time<='2026-01-01') & (ticker=='VNINDEX') & (D_RSI_Max1W/D_RSI > 1.016)  & (D_RSI_Max3M > 0.77) & (D_RSI_Max1W < 0.79) & (D_RSI_Max1W>0.6) & (D_RSI_Max1W_Close/D_RSI_Max3M_Close > 1.008) & (D_RSI_Max3M_MACD/D_RSI_Max1W_MACD>1.1) & (D_MACDdiff < 0)  & ( Close/D_RSI_Max3M_Close > 0.97) & (D_RSI_MinT3 > 0.5) & (D_CMF < 0.15)"
        }

        return df, d_filters

    def _init_percentiles(self):
        df_vnindex = self.df_all.copy()
        p3m = 100 * (df_vnindex['O3M'] - 1)

        percentiles = [0.1, 0.2, 0.8, 0.85, 0.9, 0.95]
        dict_percentiles = {
            'P3M': p3m.quantile(percentiles).values,
            'VNINDEX_PE': df_vnindex['VNINDEX_PE'].quantile(percentiles).values,
            'PE_PE_MA5Y': (df_vnindex['VNINDEX_PE'] / df_vnindex['VNINDEX_PE_MA5Y']).quantile(percentiles).values,
        }

        for i in range(60, 720, 5):
            colname = f'P{i}'
            ser = 100 * (df_vnindex['Open_1D'].shift(-i) / df_vnindex['Open_1D'] - 1)
            dict_percentiles[f'~{colname}'] = ser.quantile(percentiles).values

        df_percentiles = pd.DataFrame(dict_percentiles, index=[f'{int(p * 100)}%' for p in percentiles])

        return df_percentiles

    def _overheat(self):
        """
        Detect overheated market periods based on 3-month momentum and percentile thresholds.

        Steps:
        1. Calculate 3M momentum (P3M) and rolling 2-year high.
        2. Select candidate periods where P3M >= 90th percentile.
        3. For each candidate, extend forward until performance breaks down
           (consecutive underperformance triggers stop).
        4. Merge consecutive hot periods into groups.
        5. Enrich groups with price stats: max close, 2Y high, start/end closes.
        6. Compute Cmax_H2Y = max close / 2Y high for overheating measure.

        Returns:
            pd.DataFrame with columns:
                - start_group: start date of overheated group
                - end_group: end date of overheated group
                - start_close: close price at start
                - end_close: close price at end
                - Cmax_H2Y: ratio max close / 2Y high
        """
        df_vni = self.df_all.copy()
        df_vni['P3M'] = 100 * (df_vni['O3M'] - 1)
        df_vni['s_time'] = pd.to_datetime(df_vni['time'])
        df_vni['max_High_2Y'] = df_vni['High'].rolling(240 * 2, min_periods=240).max()

        try:
            # Select candidate overheating points
            df_hot = df_vni.query(f"P3M >= {self.percentiles.loc['90%', 'P3M']}").copy()
            df_hot['start_group'] = df_hot['s_time']
            df_hot['end_group'] = df_hot['s_time']

            # Base reference table for lookups
            df_base = df_vni[['s_time', 'time', 'Open_1D', 'Close', 'max_High_2Y']].copy()
            df_base = df_base.sort_values('s_time').reset_index(drop=True)

            for i, row in df_hot.iterrows():
                start_date = row['start_group']
                try:
                    start_idx = df_base.index[df_base['s_time'] == start_date][0]
                except:
                    # If exact match not found, fallback: nearest backward date
                    start_idx = pd.merge_asof(
                        pd.DataFrame({'s_time': [start_date]}).sort_values('s_time'),
                        df_base[['s_time']].sort_values('s_time'),
                        on='s_time', direction='backward'
                    ).index[0]

                break_count = 0

                # Iterate over forward sessions (~Pxx) to check sustainability
                # Track the last valid session used
                session_used = None

                for col in self.percentiles.columns:
                    if not col.startswith('~'):
                        continue

                    try:
                        session = int(col.split('P')[1])  # session length in days
                    except:
                        continue

                    try:
                        cur_price = df_base.iloc[start_idx + session]['Open_1D']
                        base_price = df_base.iloc[start_idx]['Open_1D']
                        ret = (cur_price / base_price - 1) * 100

                        # If return falls below 90% percentile threshold → increment break_count
                        session_used = session  # store last session checked

                        if ret <= self.percentiles.loc['90%', col]:
                            break_count += 1
                        else:
                            break_count = 0

                        # Stop if 4 consecutive breaks
                        if break_count == 4:
                            break
                    except:
                        break

                # Estimate end date for this overheating period
                if session_used is not None:
                    try:
                        session_break = session_used - break_count * 5
                        end_date = df_base.iloc[start_idx + session_break]['time']
                    except:
                        end_date = df_base.iloc[-1]['time']
                else:
                    end_date = df_base.iloc[-1]['time']

                df_hot.at[i, 'end_group'] = end_date

            # Merge consecutive months (≤ 1 month gap)
            df_hot['month'] = pd.to_datetime(df_hot['s_time'].dt.strftime('%Y-%m'))
            df_hot['month_diff'] = round(df_hot['month'].diff().dt.days / 31).fillna(0)
            df_hot['group'] = (df_hot['month_diff'] > 1).cumsum()
            df_hot['start_group'] = df_hot.groupby('group')['start_group'].transform('min')
            df_hot['end_group'] = df_hot.groupby('group')['end_group'].transform('max')
            df_hot = df_hot.drop_duplicates(subset=['group'], keep='first')

            # Merge consecutive groups if end/start gap ≤ 1 month
            df_hot['group_diff'] = round((df_hot['start_group'] - df_hot['end_group'].shift(1)).dt.days / 31).fillna(0)
            df_hot['group'] = (df_hot['group_diff'] > 1).cumsum()
            df_hot['start_group'] = df_hot.groupby('group')['start_group'].transform('min')
            df_hot['end_group'] = df_hot.groupby('group')['end_group'].transform('max')
            df_hot = df_hot.drop_duplicates(subset=['group'], keep='first').reset_index(drop=True)

            # Compute max close within each period
            price_t = df_vni[['s_time', 'Close']].sort_values('s_time').reset_index(drop=True)
            times = price_t['s_time'].to_numpy()
            closes = price_t['Close'].to_numpy()

            def range_max(t1, t2):
                i = np.searchsorted(times, np.datetime64(t1), side='left')
                j = np.searchsorted(times, np.datetime64(t2), side='right') - 1
                if j < i:
                    return np.nan
                return np.nanmax(closes[i:j + 1])

            df_hot['max_Close'] = [range_max(s, e) for s, e in zip(df_hot['start_group'], df_hot['end_group'])]

            # Map 2Y high & close prices
            map_series = df_base.set_index('s_time')['max_High_2Y']
            df_hot['max_High_2Y'] = df_hot['start_group'].map(map_series)
            df_hot['start_close'] = df_hot['start_group'].map(df_base.set_index('s_time')['Close'])
            df_hot['end_close'] = df_hot['end_group'].map(df_base.set_index('s_time')['Close'])

            # Fallback: use merge_asof if mapping failed
            na_mask = df_hot['max_High_2Y'].isna()
            if na_mask.any():
                df_check = df_base[['s_time', 'max_High_2Y', 'Close']].dropna().drop_duplicates('s_time',
                                                                                                keep='last').sort_values(
                    's_time')
                filled = pd.merge_asof(
                    df_hot.loc[na_mask, ['start_group']].sort_values('start_group'),
                    df_check, left_on='start_group', right_on='s_time', direction='backward'
                )
                filled.index = df_hot.loc[na_mask].sort_values('start_group').index
                df_hot.loc[na_mask, 'max_High_2Y'] = filled['max_High_2Y'].values
                df_hot.loc[na_mask, 'start_close'] = filled['Close'].values

            # Final overheating ratio
            df_hot['Cmax_H2Y'] = df_hot['max_Close'] / df_hot['max_High_2Y']

            return df_hot[['start_group', 'end_group', 'start_close', 'end_close', 'Cmax_H2Y']]
        except:
            print(traceback.format_exc())
            pass

    def block_score(self, block_len: int = 30):
        """
        Score sell signals based on PE ratio and overheating context.

        Steps:
        1. Select sell signals ending with "~" (monthly first occurrence).
        2. Merge with index PE and overheating periods.
        3. For each sell:
           - Default mask = 1 block (30 days).
           - If PE >= 85th percentile:
             a. If within overheating → extend to 9 blocks (~270 days).
             b. If within 1 year after overheating and profit >= 90% threshold → extend 9 blocks.
        4. Append query masks into self.masks.

        Args:
            block_len: int, number of days in one block (default=30).

        Returns:
            None (but fills self.masks with query strings).
        """
        df_sell = self.df_sell.copy()
        df_sell['time'] = pd.to_datetime(df_sell['time'])
        df_all = self.df_all.copy()
        df_all['time'] = pd.to_datetime(df_all['time'])
        overheat_df = self.overheat_df.copy()
        if not overheat_df.empty:
            for col in ['start_group', 'end_group']:
                overheat_df[col] = pd.to_datetime(overheat_df[col])

        # --- Step 1: filter sell signals ---
        df_sell_filtered = (
            df_sell[df_sell["Sell_filter"].str.endswith("~", na=False)]
            .sort_values("time")
            .assign(month=lambda x: x['time'].dt.to_period('M'))
            .drop_duplicates(subset=['month'], keep='first')
            .drop(columns=['month'])
        )

        # Merge with PE
        df_sell_filtered = df_sell_filtered.merge(
            df_all[['time', 'VNINDEX_PE']], on='time', how='left', validate='one_to_one'
        ).rename(columns={'VNINDEX_PE': 'index_pe'})

        # Merge with overheating info
        df_sell_filtered = pd.merge_asof(
            df_sell_filtered.sort_values('time'),
            overheat_df[['start_group', 'end_group', 'start_close', 'end_close']].sort_values('end_group'),
            left_on='time', right_on='start_group', direction='backward'
        ).sort_values('time')

        # PE thresholds
        try:
            pe_85 = float(self.percentiles.loc['85%', 'VNINDEX_PE'])
            pe_low = float(self.percentiles.loc['10%', 'VNINDEX_PE'])
        except:
            pe_85, pe_low = 16.87, 12.3

        # Prepare arrays for PE check
        df_all_pe = df_all[['time', 'VNINDEX_PE']].dropna().sort_values('time')
        time_all = df_all_pe['time'].to_numpy(dtype='datetime64[ns]')
        pe_all = df_all_pe['VNINDEX_PE'].to_numpy(dtype=float)

        # --- Helper function to extend mask ---
        def extend_mask(base_start: pd.Timestamp, k_blocks: int = 1):
            """
            Build query mask for a given time window.
            If PE <= pe_low before end, cut mask early.
            """
            end_time = base_start + pd.Timedelta(days=block_len * k_blocks)

            # Find id of base_start and end_time
            left = np.searchsorted(time_all,
                                   np.datetime64(base_start.tz_localize(None) if base_start.tzinfo else base_start),
                                   side='left')
            right = np.searchsorted(time_all, np.datetime64(end_time), side='left')

            if left < right:
                window_pe = pe_all[left:right]
                hit_idx_rel = np.nonzero(window_pe <= pe_low)[0]
                if hit_idx_rel.size > 0:
                    cut_time = pd.to_datetime(time_all[left + hit_idx_rel[0]])
                    end_time = cut_time

            return {
                'start': base_start,
                'end': end_time
            }

        # --- Step 2: iterate through sells ---
        for _, sell_row in df_sell_filtered.iterrows():
            sell_time = sell_row['time']
            sell_close = sell_row['Close']
            index_pe = sell_row['index_pe']
            start_group, end_group, start_close = sell_row[['start_group', 'end_group', 'start_close']]

            # Default mask = 1 block
            query_mask = extend_mask(sell_time, 1)
            #
            # if pd.isna(index_pe) or index_pe < pe_85 or pd.isna(start_group) or pd.isna(end_group):
            #     self.masks.append(query_mask)
            #     continue
            #
            # # Case A: within overheating period
            # if start_group <= sell_time <= end_group:
            #     query_mask = extend_mask(sell_time, 9)
            #
            # # Case B: within 1 year after overheating
            # delta_days_from_end = (sell_time - end_group).days
            # if 0 < delta_days_from_end < 365 and pd.notna(start_close) and start_close > 0:
            #     profit_from_start = 100.0 * (sell_close / start_close - 1.0)
            #     days_since_start = (sell_time - start_group).days
            #     rounded_period = int(round(days_since_start / 5) * 5)
            #     colname = f'~P{rounded_period}'
            #
            #     if ('90%' in self.percentiles.index) and (colname in self.percentiles.columns):
            #         p90_val = self.percentiles.loc['90%', colname]
            #         if pd.notna(p90_val) and profit_from_start >= float(p90_val):
            #             query_mask = extend_mask(sell_time, 9)
            #
            # # Append mask string
            self.masks.append(query_mask)

    def get_masks(self):
        self.block_score()

        return self.masks


class ScoreManager:
    def __init__(self, config, proba_col='proba', score_col='score', validation_probs=None, validation_labels=None):
        self.config = config
        self.validation_probs = None
        self.validation_labels = None

        self.market_eval = None
        self.score_engine = None

        self.score_col = score_col
        self.proba_col = proba_col

    def get_mapper_state(self):
        return self.score_engine.get_state() if self.score_engine else None

    def _setup_market_eval(self):
        self.market_eval = MarketEvaluation()

    def _setup_score_mapper(self, config, validation_probs=None, validation_labels=None):
        """
        Setup the score mapping pipeline.
        - If validation data available → fit using it.
        - Else if lift targets provided → derive knots from lift.
        - Else fallback to default score mapping (percentiles only).
        """
        # --- Build config ---
        cfg = ScoreEngineConfig(**config)

        # --- Create engine ---
        self.score_engine = ScoreEngine(cfg)
        # --- Fit ---
        self.score_engine.fit(validation_probs, validation_labels)

    @staticmethod
    def expr_indicators(df, full_expr):
        def safe_eval_formula(row, formula):
            """Safely evaluate formula expressions for feature engineering."""
            try:
                python_funcs = {'abs', 'min', 'max', 'round', 'sum', 'np', 'float', 'int'}
                vars_in_formula = set(re.findall(r'[A-Za-z_][A-Za-z0-9_]*', formula))
                expr = formula
                for var in vars_in_formula:
                    if var not in python_funcs and var in row:
                        expr = re.sub(rf'\b{var}\b', f'row["{var}"]', expr)
                return eval(expr,
                            {'np': np, 'abs': abs, 'min': min, 'max': max, 'sum': sum, 'round': round, 'float': float,
                             'int': int}, {'row': row})
            except ZeroDivisionError:
                return 0
            except Exception:
                return np.nan

        """Add computed indicator columns to dataframe."""
        df = df.copy()

        # First, create all missing columns with NaN
        all_full_expr = [expr for expr in full_expr if expr not in df.columns]
        for expr in all_full_expr:
            df[expr] = np.nan

        # Then compute formulas
        for formula in full_expr:
            if formula not in df.columns or df[formula].isna().all():
                try:
                    df[formula] = df.apply(lambda row: safe_eval_formula(row, formula), axis=1)
                except Exception as e:
                    print(f"Warning: Could not compute formula '{formula}': {e}")
                    df[formula] = 0.0  # Default value

        return df

    def fundamental_filter(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        Apply a fundamental screening and adjust scores.

        Notes (keeping original semantics):
        - fa_flag: True by default; rows FAILING the screen become False.
        - fa_option "A": set score = score_sell for failing rows.
        - fa_option "B": score = min(score, score_buy) for failing rows.
        """

        required = [
            "CF_OA_5Y", "OShares", "FSCORE", "NP_P0", "NP_P1", "NP_P4",
            "PCF", "PB", "PE", "ROE5Y", "ROE_Min3Y"
        ]
        missing = [c for c in required if c not in df.columns]
        if missing:
            raise KeyError(f"Missing columns: {missing}")

        # # Convert numeric columns once (invalid -> NaN so they naturally fail screening)
        # num_cols = ["CF_OA_5Y", "OShares", "FSCORE", "NP_P0", "NP_P1", "NP_P4",
        #             "PCF", "PB", "PE", "ROE5Y", "ROE_Min3Y"]
        # df[num_cols] = df[num_cols].apply(pd.to_numeric, errors="coerce")

        def safe_div(a, b):
            # Avoid division-by-zero; NaN -> fails numeric tests cleanly
            b = b.replace({0: np.nan}).astype(float)
            return a.astype(float) / b

        mask_pass = (
                (safe_div(df["CF_OA_5Y"], df["OShares"]) > 2500) &
                (df["FSCORE"] >= 3) &
                (df["NP_P0"] > 0) & (df["NP_P1"] > 0) & (df["NP_P4"] > 0) &
                (safe_div(df["NP_P0"], df["NP_P1"]) > 0) &
                (safe_div(df["NP_P0"], df["NP_P4"]) > 0) &
                (df["PCF"].between(0.4, 30.2)) &
                (df["PB"] < 4.6) &
                (df["PE"] > 0) &
                (df["ROE5Y"] > 0.05) &
                (df["ROE_Min3Y"] > 0.01)
        )

        # fa_flag: True by default; False for failing rows (same semantics as before)
        df["fa_flag"] = True
        df.loc[~mask_pass, "fa_flag"] = False

        opt = (self.config.get("fa_option") or "").upper()
        if opt == "A":
            df.loc[~mask_pass, self.score_col] = self.config["score_sell"]
        elif opt == "B":
            # Take elementwise min(score, score_buy) for failing rows
            df.loc[~mask_pass, self.score_col] = np.minimum(
                df.loc[~mask_pass, self.score_col].to_numpy(dtype="float64"),
                float(self.config["score_buy"])
            )

        return df

    def _round_score(self, df_data):
        df_data[self.score_col] = (
                np.floor(df_data[self.score_col] / self.config['step_round']) * self.config['step_round']
        )
        return df_data

    def run(self, df):
        # Prepare engine/mapping
        self._setup_market_eval()
        self._setup_score_mapper(self.config, self.validation_probs, self.validation_labels)

        # Ensure time column is datetime (UTC-naive) for accurate comparison
        if not np.issubdtype(df["time"].dtype, np.datetime64):
            df["time"] = pd.to_datetime(df["time"], errors="coerce")
        df = df[df["time"].notna()]

        # Compute scores from probabilities (vectorized)
        df[self.score_col] = self.score_engine.transform(df[self.proba_col])
        #
        # for mask in self.market_eval.get_masks():
        #     block_mask = (df['time'] >= mask['start'].strftime('%Y-%m-%d')) & (df['time'] < mask['end'].strftime('%Y-%m-%d'))
        #     df.loc[block_mask, self.score_col] = self.config["score_sell"]

        # Apply market evaluation rules using a single boolean mask
        if hasattr(self, "market_eval") and self.market_eval is not None:
            mask_sell = np.zeros(len(df), dtype=bool)
            t = df["time"].values
            # If there are many blocks, consider sorting & using searchsorted for O(n log m)
            for m in self.market_eval.get_masks():
                start = pd.to_datetime(m["start"])
                end = pd.to_datetime(m["end"])
                mask_sell |= (t >= np.datetime64(start)) & (t < np.datetime64(end))
            if mask_sell.any():
                df.loc[mask_sell, self.score_col] = self.config["score_sell"]

        # Apply fundamental screening and adjust scores
        df = self.fundamental_filter(df)

        # Round the final scores
        df = self._round_score(df)

        # Select and return final output columns (keep original order)
        out_cols = [
            "time", "ticker", "close", "price", "volume", "volume_1m_p50",
            self.score_col, "fa_flag"
        ]
        return df.loc[:, out_cols]
