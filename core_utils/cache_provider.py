# core_utils/cache_provider.py
import os
import atexit
import threading
import hashlib
from typing import Optional, Any

try:
    # EvalRedis is your Redis-based cache client
    from core_utils.redis_cache import EvalRedis
except Exception:
    # Allow import even if EvalRedis is not yet available (e.g. during tests)
    EvalRedis = None  # type: ignore


# --------------------------
# NullCache: safe fallback when <PERSON><PERSON> is unavailable
# --------------------------
class NullCache:
    """Stub cache compatible with EvalRedis API — used when Red<PERSON> is disabled or unavailable."""

    @staticmethod
    def generate_cache_key(unique_string: str) -> str:
        # Use md5 to keep key format consistent with RedisCache
        return hashlib.md5(unique_string.encode("utf-8")).hexdigest()

    # Prefix-based API
    def get_with_expiry_update(self, prefix: str, key: str):
        return None

    def set_prefix_cache(self, prefix: str, key: str, value: Any, expiry: Optional[int] = None) -> None:
        pass

    def manage_prefix_unique_cache(self, prefix: str, r_name: str, key: str) -> None:
        pass

    # Basic key-value API (compatible interface)
    def get_cache(self, key: str):
        return None

    def set_cache(self, key: str, value: Any, expiry: Optional[int] = None) -> None:
        pass

    # Utility methods
    def cache_exists(self, key: str) -> bool:
        return False

    def delete_cache(self, key: str) -> None:
        pass

    def clear_cache(self) -> None:
        pass

    def check_cache_status(self):
        return None


# --------------------------
# Provider: lazy singleton + fork-safe
# --------------------------
_LOCK = threading.Lock()
_INSTANCE = None
_PID = None


def _build_eval_redis_from_env() -> Any:
    """
    Initialize EvalRedis from environment variables.
    Supported ENV vars:
        - REDIS_HOST (default: localhost)
        - REDIS_PORT (default: 6379)
        - REDIS_DB (default: 1)
        - REDIS_MAX_CONN (optional)
        - REDIS_EXPIRY (default: 1800 seconds)
    """
    host = os.getenv("REDIS_HOST", "localhost")
    port = int(os.getenv("REDIS_PORT", "6379"))
    db = int(os.getenv("REDIS_DB", "0"))
    expiry = int(os.getenv("REDIS_EXPIRY", "3600"))

    if EvalRedis is None:
        # When Redis client is unavailable (e.g. during unit test)
        return NullCache()

    return EvalRedis(host=host, port=port, db=db, expiry=expiry)


def get_cache_singleton():
    """
    Return a singleton instance of the cache provider.
    Automatically reinitializes after a process fork (based on PID).
    Use inside BaseEval as:
        self.redis_cache = cache_service or get_cache_singleton()
    """
    global _INSTANCE, _PID
    pid = os.getpid()
    if (_INSTANCE is None) or (_PID != pid):
        with _LOCK:
            if (_INSTANCE is None) or (_PID != pid):
                _INSTANCE = _build_eval_redis_from_env()
                _PID = pid
    return _INSTANCE


def close_cache():
    """Close Redis connection if available (optional cleanup)."""
    global _INSTANCE
    try:
        if _INSTANCE is not None and hasattr(_INSTANCE, "redis_conn"):
            _INSTANCE.redis_conn.close()  # type: ignore[attr-defined]
    except Exception:
        pass


def reset_cache_for_tests(use_null: bool = True):
    """
    Reset the cache provider (used for testing).
    If `use_null=True`, replaces the Redis client with a NullCache.
    """
    global _INSTANCE, _PID
    with _LOCK:
        _INSTANCE = NullCache() if use_null else _build_eval_redis_from_env()
        _PID = os.getpid()


@atexit.register
def _cleanup_cache_provider():
    """Automatically cleanup cache connections at process exit."""
    close_cache()
