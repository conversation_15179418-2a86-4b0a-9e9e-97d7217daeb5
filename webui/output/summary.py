import os
import glob
import json
import pandas as pd

def load_v2_json_files(directory=None):
    """
    Load all v2_*.json files in the given directory, each file as a row, keys as columns.
    Args:
        directory (str): Path to directory. If None, use current file's directory.
    Returns:
        pd.DataFrame: DataFrame with each file as a row, keys as columns.
    """
    if directory is None:
        directory = os.path.dirname(os.path.abspath(__file__))
    pattern = os.path.join(directory, 'v2_*.json')
    file_list = glob.glob(pattern)
    rows = []
    for file_path in file_list:
        with open(file_path, 'r', encoding='utf-8') as f:
            try:
                data = json.load(f)
            except Exception as e:
                data = {'error': str(e)}
        data['filename'] = os.path.basename(file_path)
        rows.append(data)
    df = pd.DataFrame(rows)
    return df

df = load_v2_json_files('output/all')
df.to_csv('simulation_result.csv', index=False)