import json
from datetime import datetime
from multiprocessing import shared_memory, Pool as StandardPool

import numpy as np
import pandas as pd
from pathos.multiprocessing import ProcessingPool as PathosPool

from core_utils.simulation_v2a import WrapperSimulation
from core_utils.warp_score import ScoreManager


# ============================================================================
# Worker function for shared memory multiprocessing
# ============================================================================
def _shuffle_by_date(pd_deals: pd.DataFrame, seed: int) -> pd.DataFrame:
    """
    Shuffle DataFrame by date with random ordering within each date.

    Args:
        pd_deals: DataFrame to shuffle
        seed: Random seed for reproducibility

    Returns:
        Shuffled DataFrame
    """
    rng = np.random.RandomState(seed)
    pd_deals = pd_deals.copy()  # Avoid modifying original
    pd_deals["rand_order"] = rng.rand(len(pd_deals))

    pd_deals.sort_values(
        by=["ymd", "score", "rand_order"],
        ascending=[True, False, True],
        inplace=True
    )

    # Drop temporary column and reset index
    pd_deals.drop(columns=["rand_order"], inplace=True)
    pd_deals.reset_index(drop=True, inplace=True)

    return pd_deals


def _worker_process_simulate(args):
    """
    Worker function for multiprocessing with shared memory support.

    This function is defined at module level (not inside class) to be picklable.
    It reconstructs the DataFrame from shared memory and runs the simulation.

    Args:
        args: Tuple of (seed, shm_name, df_metadata, simulate_config)
            - seed: Random seed for this simulation run
            - shm_name: Name of shared memory block containing DataFrame data
            - df_metadata: Dict with DataFrame structure info (columns, dtypes, shape)
            - simulate_config: Simulation configuration dict

    Returns:
        Dict with simulation results
    """
    seed, shm_name, df_metadata, simulate_config = args

    # Reconstruct DataFrame from shared memory (zero-copy)
    shm = shared_memory.SharedMemory(name=shm_name)
    try:
        # Reconstruct numpy array from shared memory buffer
        np_array = np.ndarray(
            df_metadata['shape'],
            dtype=df_metadata['dtype'],
            buffer=shm.buf
        )

        # Reconstruct DataFrame with proper columns and dtypes
        df_datas = pd.DataFrame(np_array, columns=df_metadata['columns'])

        # Restore categorical dtypes for memory efficiency
        for col, dtype in df_metadata['column_dtypes'].items():
            if dtype == 'category':
                df_datas[col] = df_datas[col].astype('category')

        # Set random seed for reproducibility
        np.random.seed(seed)

        # Shuffle data efficiently
        df_shuffled = _shuffle_by_date(df_datas, seed)

        # Run simulation
        simulator = WrapperSimulation(simulate_config)
        simulator.run(df_shuffled)
        stats = simulator.stats()

        # Extract results
        # pos_df = stats['stats_tx']['pos']
        # set_ticker = pos_df['ticker'].unique().tolist()
        # set_quarter_ticker = list(set(zip(pos_df['ticker'], pos_df['open_quarter'])))

        result = stats['stats_log']
        result.update(stats['stats_tx']['tmetrics'])
        # result['set_ticker'] = set_ticker
        # result['set_quarter_ticker'] = set_quarter_ticker

        return {'output': result}

    finally:
        # Close shared memory (but don't unlink - parent process will do that)
        shm.close()


class Simulation_v2:
    """
    Simulate trading strategy
    """

    def __init__(self, simulate_config=None, score_config=None, start_date='2022-06-01',
                 end_date='2026-01-01', num_proc=20, buffer_ratio=0.16, fpath='ticker_v1a'):

        self.simulate_config = simulate_config
        self.score_config = score_config
        self.score_manager = ScoreManager(score_config, proba_col='proba', score_col='score')

        self.df_datas = None
        self.start_date = start_date
        self.end_date = self.preprocess_end_time(end_date)
        # self.period, self.buffer = self.find_simulate_period(start_date, self.end_date, buffer_ratio)

        self.num_proc = num_proc
        self.fpath = fpath

    def load_score(self, df_datas, start_date, end_date, format='%m/%d/%Y'):
        """
        Optimized load_score with vectorized operations and efficient data types.
        """
        # Avoid unnecessary copy - work with view where possible
        df = df_datas.reset_index(drop=True)

        # Convert time once and create ymd efficiently
        df['time'] = pd.to_datetime(df['time'], format=format)
        df['ymd'] = df['time'].dt.strftime('%Y-%m-%d')

        # Calculate score
        df = self.score_manager.run(df)

        # Vectorized boolean indexing - all at once
        ii = (
                (df['close'] > 0) &
                (df['price'] > 0) &
                (df['volume_1m_p50'] > 0) &
                df['score'].notna() &
                (df['ymd'] >= start_date) &
                (df['ymd'] <= end_date)
        )

        # Select columns and rename in one operation
        data = df.loc[ii, ['ymd', 'ticker', 'close', 'price', 'score', 'volume_1m_p50']].copy()
        data.rename(columns={'close': 'close_price'}, inplace=True)

        # Vectorized calculations
        data['open_price'] = data['close_price']
        data['daily_amount'] = data['volume_1m_p50'] * data['price']
        data.drop(columns=['volume_1m_p50', 'price'], inplace=True)

        # # Optimize data types for memory and speed
        data['ticker'] = data['ticker'].astype('category')
        data['ymd'] = data['ymd'].astype('category')

        # Sort once at the end
        data.sort_values('ymd', ascending=True, inplace=True)
        data.reset_index(drop=True, inplace=True)

        return data

    @staticmethod
    def preprocess_end_time(end_date):
        end_date = min(datetime.today(), datetime.strptime(end_date, "%Y-%m-%d")).strftime('%Y-%m-%d')
        return end_date

    #
    # @staticmethod
    # def find_simulate_period(start_date, end_date, ratio):
    #     """
    #         Finds the simulation period based on the given start and end dates, and a buffer.
    #
    #         The function takes the start and end dates, and a buffer value. It calculates the number of days between the end date and the start date, and subtracts the buffer value to get the simulation period.
    #
    #         Parameters:
    #             start_date (str): The start date in the format "YYYY-MM-DD".
    #             end_date (str): The end date in the format "YYYY-MM-DD".
    #             buffer (int): The buffer value to subtract from the number of days.
    #
    #         Returns:
    #             int: The simulation period in days.
    #         """
    #     delta_day = (datetime.strptime(end_date, "%Y-%m-%d") - datetime.strptime(start_date, "%Y-%m-%d")).days
    #     buffer = int(delta_day * ratio)
    #     simulate_period = int(delta_day - buffer)
    #     return simulate_period, buffer
    #
    @staticmethod
    def convert_to_dict(serial_data):
        result = {}

        for item in serial_data.items():
            key, value = item
            main_key, sub_key = key.split('.', 1)

            if main_key not in result:
                result[main_key] = {}

            result[main_key][sub_key] = value

        return result

    def _run_with_shared_memory(self, random_list):
        """
        Run simulations using shared memory for efficient multiprocessing.

        This method creates a shared memory block containing the DataFrame data,
        allowing worker processes to access it without serialization overhead.

        Args:
            random_list: List of random seeds for each iteration

        Returns:
            List of simulation results from all workers
        """
        # Convert DataFrame to numpy array for shared memory
        # Save categorical columns info before conversion
        column_dtypes = {col: str(self.df_datas[col].dtype)
                         for col in self.df_datas.columns}

        # Convert categorical columns to string for numpy compatibility
        df_for_shm = self.df_datas.copy()
        for col in df_for_shm.columns:
            if df_for_shm[col].dtype.name == 'category':
                df_for_shm[col] = df_for_shm[col].astype(str)

        # Convert to numpy array
        np_array = df_for_shm.to_numpy()
        np_array = np.ascontiguousarray(np_array)

        # Create shared memory block
        shm = shared_memory.SharedMemory(create=True, size=np_array.nbytes)

        try:
            # Copy data to shared memory
            shm_array = np.ndarray(np_array.shape, dtype=np_array.dtype, buffer=shm.buf)
            shm_array[:] = np_array[:]

            # Prepare metadata for workers to reconstruct DataFrame
            df_metadata = {
                'shape': np_array.shape,
                'dtype': np_array.dtype,
                'columns': list(self.df_datas.columns),
                'column_dtypes': column_dtypes
            }

            # Prepare arguments for each worker
            args_list = [
                (seed, shm.name, df_metadata, self.simulate_config)
                for seed in random_list
            ]

            # SOLUTION #4: Use imap_unordered for dynamic load balancing
            # This allows workers to pick up new tasks as soon as they finish,
            # preventing idle workers when some tasks take longer than others
            # Note: Using standard multiprocessing.Pool instead of pathos for imap_unordered support
            with StandardPool(processes=self.num_proc) as pool:
                all_results = list(pool.imap_unordered(
                    _worker_process_simulate,
                    args_list,
                    chunksize=1  # Dynamic assignment - workers get tasks one at a time
                ))

            return all_results

        finally:
            # Cleanup: close and unlink shared memory
            shm.close()
            shm.unlink()

    def process_simulate(self, seed):
        """
        This method is used when use_shared_memory=False in run_fast().
        """
        np.random.seed(seed)

        # Shuffle data efficiently
        df_datas = _shuffle_by_date(self.df_datas, seed)

        # Run simulation
        simulator = WrapperSimulation(self.simulate_config)
        simulator.run(df_datas)
        stats = simulator.stats()

        # pos_df = stats['stats_tx']['pos']
        # set_ticker = pos_df['ticker'].unique().tolist()

        # set_quarter_ticker = list(set(zip(pos_df['ticker'], pos_df['open_quarter'])))

        result = stats['stats_log']
        result.update(stats['stats_tx']['tmetrics'])
        # result['set_ticker'] = set_ticker
        # result['set_quarter_ticker'] = set_quarter_ticker

        return {'output': result}

    def run_fast(self, df_proba, iterate=10, use_shared_memory=True):
        """
        Run multiple simulation iterations in parallel.

        Args:
            df_proba: DataFrame with probability/score data
            iterate: Number of simulation iterations to run
            use_shared_memory: If True, use shared memory optimization
                             If False, use original method (for backward compatibility)

        Returns:
            Dict with aggregated simulation results
        """
        # Load and prepare data
        self.df_datas = self.load_score(df_proba, start_date=self.start_date, end_date=self.end_date, format='%Y-%m-%d')
        np.random.seed(iterate)
        random_list = list(range(iterate))

        if use_shared_memory:
            all_results = self._run_with_shared_memory(random_list)
        else:
            # Use method for small datasets
            with PathosPool(processes=self.num_proc) as pool:
                all_results = pool.map(self.process_simulate, random_list)

        # Normalize results
        df = pd.json_normalize(all_results)

        # Pre-allocate result series for better performance
        df_result = pd.Series(dtype=object)

        # Vectorized aggregation operations
        for col in df.columns:
            if 'CAGR' in col:
                # Compute all CAGR-related metrics at once
                col_data = df[col]
                df_result[col] = col_data.mean()
                df_result[col.replace('CAGR', 'return_std')] = col_data.std()
                df_result[col.replace('CAGR', 'return_max')] = col_data.max()
                df_result[col.replace('CAGR', 'return_min')] = col_data.min()

            elif ('set_ticker' in col) or ('set_quarter_ticker' in col):
                # Optimized ticker diversity calculation
                col_values = df[col].values
                lengths = [len(v) for v in df[col].values]
                tickers = [ticker for v in df[col].values for ticker in v]
                df_result[col] = len(set(tickers)) / np.mean(lengths)
                df_result[f"{col}_diversity"] = len(set(tickers)) / np.mean(lengths)
                df_result[col.replace('set', 'unique')] = len(set(tickers))
            else:
                # Simple mean for other columns
                df_result[col] = df[col].mean()

        return self.convert_to_dict(df_result)['output']

    def get_detail(self):
        """
        Optimized get_detail with vectorized shuffle operation.
        """

        def shuffle_by_date(pd_deals: pd.DataFrame, seed: int) -> pd.DataFrame:
            rng = np.random.RandomState(seed)
            pd_deals = pd_deals.copy()
            pd_deals["_shuffle_key"] = rng.rand(len(pd_deals))

            # Sort by date and random key (vectorized operation)
            pd_deals.sort_values(by=["ymd", "_shuffle_key"], inplace=True)
            pd_deals.drop(columns=["_shuffle_key"], inplace=True)
            pd_deals.reset_index(drop=True, inplace=True)

            return pd_deals

        df_datas = shuffle_by_date(self.df_datas, 42)

        simulator = WrapperSimulation(self.simulate_config)
        simulator.run(df_datas)
        result = simulator.stats()
        return result


if __name__ == "__main__":
    import os
    import sys

    current_dir = os.path.dirname(os.path.abspath(__file__))
    current_dir = current_dir.replace("/webui", "")
    os.chdir(current_dir)
    sys.path.insert(0, current_dir)
    #
    FPATH = 'ticker_v1a'
    simulate_config = {
        'initial_amount': 1e9,
        'cutloss': 0.2,
        'cutloss_duration': 15,  # number of days not buying after cutloss
        'ratio_nav': 1.0,  # ratio between invest amount and nav (we will not buy more than ratio_nav * nav)
        'ratio_deal': 0.1,
        # ratio between each deal amount and invest amount (we will not buy more than ratio_deal * ratio_nav * nav))
        'ratio_deal_volume': 0.1,
        # ratio between each deal and daily volume (we will not buy more than ratio_deal_volume * daily_volume * price_target)
        'review_frequency': 'monthly',
        'fee_buy_rate': 0.001,
        'fee_sell_rate': 0.002,
        'score_sell': 0,
        'score_buy': 1,
        'gamma': 1,
        'min_ratio_deal_nav': 0.01,  # for buy or partial sell
        'verbose': True,
    }

    score_config = {
        "score_col": "score",
        "proba_col": "proba",
        "step_round": 0.2,
        "score_sell": -1,
        "score_buy": 1,
        "fa_option": "B",
        'calibrate_kind': None,
        'use_temperature': False,
        'temp_mode': 'brier',
        'percentiles': (0, 20, 40, 60, 80, 100),
        'score_knots': None,
        'lift_target_rates': None,
        'base_rate': None,
        'clip_range': (-1.0, 3.0)
    }

    df_proba = pd.read_csv('webui/score_v2.csv',
                           usecols=['time', 'ticker', 'close', 'price', 'volume', 'volume_1m_p50', 'proba', "CF_OA_5Y",
                                    "OShares", "FSCORE", "NP_P0", "NP_P1", "NP_P4", "PCF", "PB", "PE", "ROE5Y",
                                    "ROE_Min3Y"])

    # df_proba = pd.read_csv('webui/score_v1.csv')
    # df_proba.rename(columns={'volume_p50_1m': 'volume_1m_p50'}, inplace=True)
    # df_proba = pd.read_csv('core_utils/score/predictions_2.csv')
    # df_proba = df_proba[df_proba['time'] >= '2025-06-01'].reset_index(drop=True)
    # df_proba.to_csv('webui/score_v2.csv', index=False)
    # df_proba = pd.read_csv('webui/score_v2.csv')

    simulation = Simulation_v2(simulate_config, score_config, start_date='2025-06-01')
    result = simulation.run_fast(df_proba, iterate=2, use_shared_memory=True)
    #
    # with open('result.json', 'w') as f:
    #     json.dump(result, f, indent=4)
