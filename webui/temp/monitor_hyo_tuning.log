Commits on Jan 23, 2025
run sell hyopropt with new loss function
Commits on Jan 21, 2025
update config
add distribute in weight evaluate
Commits on Jan 20, 2025
improve miss data financial report
add simulate log table
Commits on Jan 19, 2025
update financial report every 12AM
Commits on Jan 18, 2025
update config
add P6M to webui
change calculator P1W..2Y with Open price
change calculator P1W..2Y with Open
quarter_ticker diversity
Commits on Jan 17, 2025
add C,L,H,O 6M
add C,L,H,O 6M
add weight evaluation streamlit ui
Commits on Jan 16, 2025
update auto run update report
update new compute ticker_diversity
update requirements.txt
Commits on Jan 15, 2025
update base_eval
Commits on Jan 14, 2025
remove old main() in webui
update new ui and refactor code
update webui/utils using base_eval.py
add config
Commits on Jan 13, 2025
add st_evaluate_by_weight
fix wrong CMB dif Amibroker
Commits on Jan 12, 2025
improve experiment.py
update hyo_tuning_sell_pattern.py
Commits on Jan 11, 2025
add si_ticker_diversity
Commits on Jan 10, 2025
updare config
hot fix html render
add cutloss table to email report
Commits on Jan 9, 2025
update config
update alert build report
ADD VOL_1M-6M_P50
update filter
Commits on Jan 8, 2025
update base_eval
add hyperopt for sell
add hyperopt for sell
add log to weight simulation
Commits on Jan 7, 2025
update filter
ignore O1W..2Y
update config
Commits on Jan 3, 2025
add Volume_3M_P80
improve hyperopt buy pattern
update new filter.json
add config
update filter to 2026
Commits on Jan 2, 2025
add custom plot to streamlit
add config 2/1/25
add custom plot to streamlit
update html sample
update new logic calculate win_quarter
update html emil report
update config.json
