# transactions_analysis.py

import pandas as pd
import numpy as np
from typing import Tuple, List, Optional, Dict


# ============================================================
# 0) Parsing & preparation
# ============================================================

def prepare_transactions(df_tx: pd.DataFrame) -> pd.DataFrame:
    """
    Normalize raw transaction logs for trade/position analytics.

    Assumptions
    -----------
    - Each row is a fill in the simulation (buy or sell).
    - Money columns are *notional values in currency units*:
        * buy_amount : total cost (price * qty) paid when buying (>=0, else 0)
        * sell_amount: total proceeds (price * qty) received when selling (>=0, else 0)
        * fee        : transaction fee in currency units (>=0)
    - `adj_price` is the executed price adjusted for splits/dividends if applicable.
    - `holding_id` ties a sequence of buys/sells into a single lifecycle (position).
    - `action` ∈ {'buy', 'sell'} (case-insensitive).
    - `ymd` is a YYYY-MM-DD string.

    What it does
    ------------
    - Parse date, lowercase actions.
    - Enforce numeric types and non-negativity on money columns.
    - Estimate fill quantity = notional / adj_price (best-effort).
    - Compute signed cashflow (+ for sells, - for buys, minus fees).
    - Provide a few handy columns for later grouping.

    Returns
    -------
    pd.DataFrame
        Cleaned dataframe sorted by date with:
        - 'date' (DatetimeIndex)
        - 'action' in {'buy','sell'}
        - 'buy_amount','sell_amount','fee','adj_price'
        - 'est_qty' : estimated shares for this fill
        - 'cashflow': +sell_amount - buy_amount - fee
    """
    out = df_tx.copy()

    # 1) Date & action normalization
    if 'ymd' in out.columns:
        out['date'] = pd.to_datetime(out['ymd'])
    elif 'date' in out.columns:
        out['date'] = pd.to_datetime(out['date'])
    else:
        raise ValueError("Missing 'ymd' or 'date' column.")

    if 'action' not in out.columns:
        raise ValueError("Missing 'action' column.")
    out['action'] = out['action'].str.lower().str.strip()

    # 2) Ensure money/price columns exist and are numeric
    for col in ['buy_amount', 'sell_amount', 'fee', 'adj_price']:
        if col not in out.columns:
            out[col] = 0.0
        out[col] = pd.to_numeric(out[col], errors='coerce').fillna(0.0)

    # Clip to avoid negatives in these fields (simulation logs should be non-negative)
    for col in ['buy_amount', 'sell_amount', 'fee']:
        out[col] = out[col].clip(lower=0.0)

    # 3) Estimate per-fill quantity:
    #    If this is a buy, use buy_amount / price; if sell, use sell_amount / price.
    #    If both amounts present (>0), prefer the one matching the action.
    def _est_qty(row):
        price = row['adj_price'] if row['adj_price'] > 0 else np.nan
        if pd.isna(price):
            return np.nan
        if row['action'] == 'buy':
            amt = row['buy_amount'] if row['buy_amount'] > 0 else row['sell_amount']
        else:
            amt = row['sell_amount'] if row['sell_amount'] > 0 else row['buy_amount']
        return amt / price if price > 0 else np.nan

    out['est_qty'] = out.apply(_est_qty, axis=1)

    # 4) Signed cashflow (+ for sells, - for buys, subtract fee)
    out['cashflow'] = out['sell_amount'] - out['buy_amount'] - out['fee']

    # 5) Sort & index
    out = out.sort_values(['date', 'holding_id']).reset_index(drop=True)
    out.set_index('date', inplace=True)

    # Basic sanity
    req_cols = {'holding_id', 'ticker', 'action'}
    missing = req_cols - set(out.columns)
    if missing:
        raise ValueError(f"Missing required columns: {missing}")

    return out


# ============================================================
# 1) Position builder (group by holding_id)
# ============================================================

def build_positions(df_tx: pd.DataFrame, qty_tol: float = 1e-8) -> pd.DataFrame:
    """
    Aggregate fills into position-level rows using `holding_id`.

    Lifecycle logic
    ---------------
    - Group rows by `holding_id` and sort by date.
    - Position open_date = first fill date; close_date = last fill date.
    - Buys add positive quantity; sells subtract quantity (best-effort via est_qty).
    - If net quantity ≈ 0 (within `qty_tol`), position is considered CLOSED.
      Otherwise it's OPEN (unrealized PnL not computed here due to missing mark price).

    PnL & pricing
    -------------
    - gross_pnl = sum(sell_amount) - sum(buy_amount)
    - net_pnl   = gross_pnl - sum(fee)
    - invested  = sum(buy_amount)  (capital deployed)
    - ret_net   = net_pnl / invested  (NaN if invested==0)
    - avg_buy_price  = sum(buy_amount)  / sum(buy_qty)  (if buy_qty>0)
    - avg_sell_price = sum(sell_amount) / sum(sell_qty) (if sell_qty>0)

    Returns
    -------
    pd.DataFrame
        One row per holding_id with:
        - ticker, open_date, close_date, trades_count
        - buy_notional, sell_notional, fee_total
        - buy_qty, sell_qty, qty_net, status ('closed'|'open')
        - gross_pnl, net_pnl, invested, ret_net
        - avg_buy_price, avg_sell_price
        - holding_days (inclusive)
    """
    g = df_tx.groupby('holding_id', sort=False)

    rows = []
    for hid, grp in g:
        grp = grp.sort_index()

        ticker = grp['ticker'].iloc[0]
        open_date = grp.index.min()
        close_date = grp.index.max()
        trades_count = len(grp)

        # Quantities by action (best-effort)
        buy_mask = grp['action'].eq('buy')
        sell_mask = grp['action'].eq('sell')

        buy_notional = grp.loc[buy_mask, 'buy_amount'].sum()
        sell_notional = grp.loc[sell_mask, 'sell_amount'].sum()
        fee_total = grp['fee'].sum()

        buy_qty = grp.loc[buy_mask, 'est_qty'].sum(min_count=1)
        sell_qty = grp.loc[sell_mask, 'est_qty'].sum(min_count=1)

        if pd.isna(buy_qty): buy_qty = 0.0
        if pd.isna(sell_qty): sell_qty = 0.0

        qty_net = buy_qty - sell_qty
        status = 'closed' if abs(qty_net) <= qty_tol else 'open'

        gross_pnl = sell_notional - buy_notional
        net_pnl = gross_pnl - fee_total

        invested = buy_notional if buy_notional > 0 else np.nan
        ret_net = (net_pnl / invested) if pd.notna(invested) and invested != 0 else np.nan

        avg_buy_price = (buy_notional / buy_qty) if buy_qty > 0 else np.nan
        avg_sell_price = (sell_notional / sell_qty) if sell_qty > 0 else np.nan

        holding_days = (close_date - open_date).days + 1

        rows.append({
            'holding_id': hid,
            'ticker': ticker,
            'open_date': open_date,
            'close_date': close_date,
            'trades_count': trades_count,
            'buy_notional': buy_notional,
            'sell_notional': sell_notional,
            'fee_total': fee_total,
            'buy_qty': buy_qty,
            'sell_qty': sell_qty,
            'qty_net': qty_net,
            'status': status,
            'gross_pnl': gross_pnl,
            'net_pnl': net_pnl,
            'invested': invested,
            'ret_net': ret_net,
            'avg_buy_price': avg_buy_price,
            'avg_sell_price': avg_sell_price,
            'holding_days': holding_days
        })

    pos = pd.DataFrame(rows).sort_values(['open_date', 'holding_id']).reset_index(drop=True)
    # Helpful time fields
    pos['open_month'] = pos['open_date'].dt.to_period('M').astype(str)
    pos['close_month'] = pos['close_date'].dt.to_period('M').astype(str)
    return pos


# ============================================================
# 2) Trade-level metrics (from positions)
# ============================================================

def trade_metrics(pos: pd.DataFrame, closed_only: bool = True) -> pd.Series:
    """
    Compute headline trade-performance metrics from position table.

    Metrics (closed-only by default)
    --------------------------------
    - n_trades, win_rate
    - avg_win, avg_loss
    - profit_factor = sum(win_pnl) / abs(sum(loss_pnl))
    - expectancy    = p(win)*avg_win - p(loss)*avg_loss
    - median_pnl, p25, p75, p90
    - holding_days_mean/median

    Returns
    -------
    pd.Series
    """
    df = pos.copy()
    if closed_only:
        df = df[df['status'] == 'closed'].copy()

    pnl = df['net_pnl'].dropna()
    n = len(pnl)
    if n == 0:
        return pd.Series({
            'n_trades': 0, 'win_rate': np.nan,
            'avg_win': np.nan, 'avg_loss': np.nan,
            'profit_factor': np.nan, 'expectancy': np.nan,
            'median_pnl': np.nan, 'p25_pnl': np.nan, 'p75_pnl': np.nan, 'p90_pnl': np.nan,
            'holding_days_mean': np.nan, 'holding_days_median': np.nan
        })

    wins = pnl[pnl > 0]
    losses = pnl[pnl < 0]

    p_win = len(wins) / n
    p_loss = len(losses) / n

    avg_win = wins.mean() if len(wins) > 0 else 0.0
    avg_loss = losses.mean() if len(losses) > 0 else 0.0  # negative

    profit_factor = (wins.sum() / abs(losses.sum())) if len(losses) > 0 else np.inf
    expectancy = p_win * avg_win + p_loss * avg_loss  # avg_loss is negative

    holding_days_mean = df['holding_days'].mean() if 'holding_days' in df.columns else np.nan
    holding_days_median = df['holding_days'].median() if 'holding_days' in df.columns else np.nan

    return pd.Series({
        'n_trades': n,
        'win_rate': p_win,
        'avg_win': avg_win,
        'avg_loss': avg_loss,
        'profit_factor': profit_factor,
        'expectancy': expectancy,
        'median_pnl': pnl.median(),
        'p25_pnl': pnl.quantile(0.25),
        'p75_pnl': pnl.quantile(0.75),
        'p90_pnl': pnl.quantile(0.90),
        'holding_days_mean': holding_days_mean,
        'holding_days_median': holding_days_median
    })


# ============================================================
# 3) Cost & turnover analytics
# ============================================================

def cost_metrics(df_tx: pd.DataFrame) -> pd.Series:
    """
    Summarize fees and rough turnover from raw fills.

    Definitions
    -----------
    - total_fee     : sum of 'fee'
    - gross_notional: sum(buy_amount + sell_amount) (both directions)
    - fee_bps       : total_fee / gross_notional * 10,000 (basis points)
    - turnover      : here simply gross_notional (absolute activity proxy)

    Note
    ----
    If you have NAV per-day, you can compute more informative *turnover ratios*
    like (gross_notional / average NAV) by day/month; this function returns
    a global snapshot.
    """
    total_fee = df_tx['fee'].sum()
    gross_notional = (df_tx['buy_amount'] + df_tx['sell_amount']).sum()
    fee_bps = (total_fee / gross_notional * 1e4) if gross_notional > 0 else np.nan

    return pd.Series({
        'total_fee': total_fee,
        'gross_notional': gross_notional,
        'fee_bps': fee_bps
    })


def monthly_turnover(df_tx: pd.DataFrame) -> pd.DataFrame:
    """
    Monthly aggregation of activity & fees (useful for dashboards).

    Returns
    -------
    pd.DataFrame
        index: 'YYYY-MM'
        columns: buy_notional, sell_notional, gross_notional, fee_total
    """
    tmp = df_tx.copy()
    tmp['month'] = tmp.index.to_period('M').astype(str)
    grp = tmp.groupby('month', sort=True).agg(
        buy_notional=('buy_amount', 'sum'),
        sell_notional=('sell_amount', 'sum'),
        fee_total=('fee', 'sum'),
    )
    grp['gross_notional'] = grp['buy_notional'] + grp['sell_notional']
    return grp[['buy_notional', 'sell_notional', 'gross_notional', 'fee_total']]


# ============================================================
# 4) PnL attribution
# ============================================================

def attrib_by(pos: pd.DataFrame, by: List[str]) -> pd.DataFrame:
    """
    Aggregate net PnL by arbitrary dimensions (e.g., ticker, month).

    Example
    -------
    attrib_by(pos, by=['ticker'])
    attrib_by(pos, by=['ticker', 'close_month'])

    Returns
    -------
    pd.DataFrame
        columns: net_pnl, trades, win_rate
    """
    df = pos.copy()
    df['is_win'] = (df['net_pnl'] > 0).astype(int)
    grp = df.groupby(by, dropna=False).agg(
        net_pnl=('net_pnl', 'sum'),
        trades=('net_pnl', 'count'),
        wins=('is_win', 'sum')
    ).reset_index()
    grp['win_rate'] = grp['wins'] / grp['trades']
    return grp.drop(columns=['wins']).sort_values('net_pnl', ascending=False)


# ============================================================
# 5) High-level pipeline
# ============================================================

def analyze_transactions(df_tx_raw: pd.DataFrame) -> Dict[str, pd.DataFrame]:
    """
    One-call convenience wrapper that:
    - prepares transactions
    - builds positions
    - computes trade metrics, cost metrics, monthly turnover
    - returns a dictionary of key tables/series

    Returns
    -------
    dict with keys:
      - 'tx'       : prepared transactions dataframe
      - 'pos'      : position table
      - 'tmetrics' : trade metrics (Series -> DataFrame)
      - 'cmetrics' : cost metrics  (Series -> DataFrame)
      - 'turnover' : monthly turnover table
      - 'attrib_ticker' : PnL by ticker
      - 'attrib_month'  : PnL by close_month
    """
    tx = prepare_transactions(df_tx_raw)
    pos = build_positions(tx)

    tmetrics = trade_metrics(pos, closed_only=True).to_frame(name='value')
    cmetrics = cost_metrics(tx).to_frame(name='value')
    turn = monthly_turnover(tx)

    attrib_ticker = attrib_by(pos, by=['ticker'])
    attrib_month = attrib_by(pos, by=['close_month'])

    return {
        'tx': tx,
        'pos': pos,
        'tmetrics': tmetrics,
        'cmetrics': cmetrics,
        'turnover': turn,
        'attrib_ticker': attrib_ticker,
        'attrib_month': attrib_month
    }


# ============================================================
# 6) Minimal example (remove in production)
# ============================================================
if __name__ == "__main__":
    # Tiny demo with 2 positions (1 closed, 1 open)
    demo = pd.DataFrame([
        {'action': 'buy',  'adj_price': 10000.0, 'buy_amount': 50_000_000, 'fee': 20_000, 'holding_id': 'A', 'sell_amount': 0.0, 'ticker': 'AAA', 'ymd': '2022-06-01'},
        {'action': 'buy',  'adj_price':  9900.0, 'buy_amount': 30_000_000, 'fee': 15_000, 'holding_id': 'A', 'sell_amount': 0.0, 'ticker': 'AAA', 'ymd': '2022-06-02'},
        {'action': 'sell', 'adj_price': 11000.0, 'buy_amount': 0.0,        'fee': 25_000, 'holding_id': 'A', 'sell_amount': 60_000_000, 'ticker': 'AAA', 'ymd': '2022-06-10'},
        {'action': 'sell', 'adj_price': 11200.0, 'buy_amount': 0.0,        'fee': 25_000, 'holding_id': 'A', 'sell_amount': 35_000_000, 'ticker': 'AAA', 'ymd': '2022-06-13'},

        {'action': 'buy',  'adj_price':  3700.0, 'buy_amount': 30_272_830, 'fee': 18_000, 'holding_id': 'B', 'sell_amount': 0.0, 'ticker': 'BVG', 'ymd': '2022-06-16'},
        {'action': 'sell', 'adj_price':  3900.0, 'buy_amount': 0.0,        'fee': 18_000, 'holding_id': 'B', 'sell_amount': 24_349_885, 'ticker': 'BVG', 'ymd': '2022-06-20'},
        # position B still open if net qty > 0 (depends on amounts/prices)
    ])

    results = analyze_transactions(demo)
    print("\n== POSITIONS ==")
    print(results['pos'].round(4))

    print("\n== TRADE METRICS ==")
    print(results['tmetrics'].round(4))

    print("\n== COST METRICS ==")
    print(results['cmetrics'].round(4))

    print("\n== MONTHLY TURNOVER ==")
    print(results['turnover'].round(2))

    print("\n== ATTRIB TICKER ==")
    print(results['attrib_ticker'].round(2))

    print("\n== ATTRIB BY CLOSE MONTH ==")
    print(results['attrib_month'].round(2))
