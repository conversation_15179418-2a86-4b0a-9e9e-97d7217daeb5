# -*- coding: utf-8 -*-
"""
Parse các mệnh đề lọc từ dict/json sang các form:
  - A/B op x
  - A+B op x
  - A*B op x
  - A-B op x
  - A op x
Hỗ trợ đầy đủ các toán tử: >, >=, <, <=
Tự chuẩn hoá các dạng A op k*B, k*B op A về ratio A/B op k.
Tính ngưỡng chặn (lower từ >,>= ; upper từ <,<=). Với ratio sẽ suy luận bound cho nghịch đảo.

Cách chạy:
  python parse_filters.py --input filters.json --outdir ./out

File input là JSON object {key: expr_string}. <PERSON><PERSON> thể dùng sample bên dưới nếu không truyền --input.
"""

import re
import json
import argparse
from collections import defaultdict
import pandas as pd
from pathlib import Path

# ---------- regex ----------
NUM = r'(?:\d+(?:\.\d+)?(?:[eE][+\-]?\d+)?|\d*\.\d+(?:[eE][+\-]?\d+)?)'
VAR = r'([A-Za-z_][A-Za-z0-9_]*)'
OPS = r'(<=|<|>=|>)'

PAT_RATIO_LR = re.compile(rf'^{VAR}\s*/\s*{VAR}\s*{OPS}\s*({NUM})$')
PAT_SUM_LR = re.compile(rf'^{VAR}\s*\+\s*{VAR}\s*{OPS}\s*({NUM})$')
PAT_PROD_LR = re.compile(rf'^{VAR}\s*\*\s*{VAR}\s*{OPS}\s*({NUM})$')
PAT_DIFF_LR = re.compile(rf'^{VAR}\s*-\s*{VAR}\s*{OPS}\s*({NUM})$')
PAT_VAR_LR = re.compile(rf'^{VAR}\s*{OPS}\s*({NUM})$')

PAT_RATIO_RL = re.compile(rf'^({NUM})\s*{OPS}\s*{VAR}\s*/\s*{VAR}$')
PAT_SUM_RL = re.compile(rf'^({NUM})\s*{OPS}\s*{VAR}\s*\+\s*{VAR}$')
PAT_PROD_RL = re.compile(rf'^({NUM})\s*{OPS}\s*{VAR}\s*\*\s*{VAR}$')
PAT_DIFF_RL = re.compile(rf'^({NUM})\s*{OPS}\s*{VAR}\s*-\s*{VAR}$')
PAT_VAR_RL = re.compile(rf'^({NUM})\s*{OPS}\s*{VAR}$')

PAT_A_kB = re.compile(rf'^{VAR}\s*{OPS}\s*({NUM})\s*\*\s*{VAR}$')
PAT_kB_A = re.compile(rf'^({NUM})\s*\*\s*{VAR}\s*{OPS}\s*{VAR}$')


def clean_clause(s: str) -> str:
    s = s.strip()
    # bóc 1 lớp ngoặc ngoài nếu có
    if s.startswith('(') and s.endswith(')'):
        s = s[1:-1].strip()
    return re.sub(r'\s+', ' ', s)


def outer_only(s: str) -> bool:
    """Chỉ cho phép 1 lớp ngoặc ngoài; nếu còn ngoặc bên trong => loại (tránh abs(), (B+1), ...)."""
    t = s.strip()
    if t.startswith('(') and t.endswith(')'):
        t = t[1:-1].strip()
    return ('(' not in t) and (')' not in t)


def reverse_op(op: str) -> str:
    return {'>': '<', '<': '>', '>=': '<=', '<=': '>='}[op]


def expand_macros(expr: str, env: dict) -> str:
    # thay {Init} -> env['Init'] nếu có
    out = expr
    for k, v in env.items():
        out = out.replace(f'{{{k}}}', env.get(k, ''))
    return out


def parse_expressions(data: dict):
    records = []
    for key, expr in data.items():
        expanded = expand_macros(expr, data)
        # tách theo & và | (dataset này đủ an toàn)
        parts = re.split(r'\s*[&|]\s*', expanded)
        for raw in parts:
            c = clean_clause(raw)
            if not c or not outer_only(c):
                continue
            # loại các điều kiện không thuộc phạm vi xử lý
            if "==" in c or "!=" in c or "ticker" in c or "abs" in c or "'" in c or '"' in c:
                continue
            matched = False
            # 1) A/B op x
            m = PAT_RATIO_LR.match(c)
            if m:
                A, B, op, x = m.group(1), m.group(2), m.group(3), float(m.group(4))
                records.append({'rule': key, 'clause': c, 'expr': f'{A}/{B}', 'op': op, 'x': x, 'class': 'ratio'})
                matched = True
            # 1b) x op A/B
            if not matched:
                m = PAT_RATIO_RL.match(c)
                if m:
                    x, op, A, B = float(m.group(1)), m.group(2), m.group(3), m.group(4)
                    records.append(
                        {'rule': key, 'clause': c, 'expr': f'{A}/{B}', 'op': reverse_op(op), 'x': x, 'class': 'ratio'})
                    matched = True
            # 2) A+B op x
            if not matched:
                m = PAT_SUM_LR.match(c)
                if m:
                    A, B, op, x = m.group(1), m.group(2), m.group(3), float(m.group(4))
                    records.append({'rule': key, 'clause': c, 'expr': f'{A}+{B}', 'op': op, 'x': x, 'class': 'sum'})
                    matched = True
            if not matched:
                m = PAT_SUM_RL.match(c)
                if m:
                    x, op, A, B = float(m.group(1)), m.group(2), m.group(3), m.group(4)
                    records.append(
                        {'rule': key, 'clause': c, 'expr': f'{A}+{B}', 'op': reverse_op(op), 'x': x, 'class': 'sum'})
                    matched = True
            # 3) A*B op x
            if not matched:
                m = PAT_PROD_LR.match(c)
                if m:
                    A, B, op, x = m.group(1), m.group(2), m.group(3), float(m.group(4))
                    records.append({'rule': key, 'clause': c, 'expr': f'{A}*{B}', 'op': op, 'x': x, 'class': 'prod'})
                    matched = True
            if not matched:
                m = PAT_PROD_RL.match(c)
                if m:
                    x, op, A, B = float(m.group(1)), m.group(2), m.group(3), m.group(4)
                    records.append(
                        {'rule': key, 'clause': c, 'expr': f'{A}*{B}', 'op': reverse_op(op), 'x': x, 'class': 'prod'})
                    matched = True
            # 4) A-B op x
            if not matched:
                m = PAT_DIFF_LR.match(c)
                if m:
                    A, B, op, x = m.group(1), m.group(2), m.group(3), float(m.group(4))
                    records.append({'rule': key, 'clause': c, 'expr': f'{A}-{B}', 'op': op, 'x': x, 'class': 'diff'})
                    matched = True
            if not matched:
                m = PAT_DIFF_RL.match(c)
                if m:
                    x, op, A, B = float(m.group(1)), m.group(2), m.group(3), m.group(4)
                    records.append(
                        {'rule': key, 'clause': c, 'expr': f'{A}-{B}', 'op': reverse_op(op), 'x': x, 'class': 'diff'})
                    matched = True
            # 5) A op x
            if not matched:
                m = PAT_VAR_LR.match(c)
                if m:
                    A, op, x = m.group(1), m.group(2), float(m.group(3))
                    records.append({'rule': key, 'clause': c, 'expr': A, 'op': op, 'x': x, 'class': 'var'})
                    matched = True
            if not matched:
                m = PAT_VAR_RL.match(c)
                if m:
                    x, op, A = float(m.group(1)), m.group(2), m.group(3)
                    records.append({'rule': key, 'clause': c, 'expr': A, 'op': reverse_op(op), 'x': x, 'class': 'var'})
                    matched = True
            # 6) A op k*B  hoặc k*B op A  -> A/B op k
            if not matched:
                m = PAT_A_kB.match(c)
                if m:
                    A, op, k, B = m.group(1), m.group(2), float(m.group(3)), m.group(4)
                    records.append({'rule': key, 'clause': c, 'expr': f'{A}/{B}', 'op': op, 'x': k, 'class': 'ratio'})
                    matched = True
            if not matched:
                m = PAT_kB_A.match(c)
                if m:
                    k, B, op, A = float(m.group(1)), m.group(2), m.group(3), m.group(4)
                    records.append(
                        {'rule': key, 'clause': c, 'expr': f'{A}/{B}', 'op': reverse_op(op), 'x': k, 'class': 'ratio'})
                    matched = True
            # Others -> bỏ qua
    return records


def compute_bounds(records):
    bounds = defaultdict(lambda: {'lower': [], 'upper': []})
    for r in records:
        expr, op, x = r['expr'], r['op'], float(r['x'])
        if op in ('>', '>='):
            bounds[expr]['lower'].append(x)
        elif op in ('<', '<='):
            bounds[expr]['upper'].append(x)

    # Suy luận nghịch đảo cho ratio
    def invert_expr(e: str):
        if '/' in e and all(t.isidentifier() for t in e.split('/')):
            a, b = e.split('/')
            return f'{b}/{a}'
        return None

    for expr, bu in list(bounds.items()):
        if '/' in expr:
            inv = invert_expr(expr)
            if inv and bu['lower']:
                for L in bu['lower']:
                    if L != 0:
                        bounds[inv]['upper'].append(1.0 / L)
            if inv and bu['upper']:
                for U in bu['upper']:
                    if U != 0:
                        bounds[inv]['lower'].append(1.0 / U)
    final = []
    for expr, bu in bounds.items():
        lower = min(bu['lower']) if bu['lower'] else None
        upper = max(bu['upper']) if bu['upper'] else None
        final.append({'expr': expr, 'lower_bound': lower, 'upper_bound': upper})
    return final


def main():
    ap = argparse.ArgumentParser()
    ap.add_argument('--input', type=str, default=None, help='Path tới file JSON chứa {key: expr}')
    ap.add_argument('--outdir', type=str, default='.', help='Thư mục xuất csv')
    args = ap.parse_args()

    # sample fallback nếu không truyền input
    sample = {
        "Init": "(Volume_1M_P50*Price>1e+9) & (time>='2014-01-01') & (time<='2026-01-01')",
        "_BKMA200": "{Init} &((ID_LO_3Y-ID_HI_3Y)>250.0) & (MA50/MA200>0.87) & (MA10/MA200<1.48) & (ROE5Y >0.115) & (PE <11.6) & (NP_P0 > 1.1*NP_P1) & (NP_P1 > 0) & (HI_3M_T1/LO_3M_T1<2.1)",
        "_TrendingGrowth": "{Init} &(Close> 1.0*Volume_Max5Y_High) & (ROE_Min5Y > 0.04)&(PE<=10.2)& (NP_P0 > 1.15*NP_P1) & (NP_P1 > NP_P2) & (PE >2.4)& (HI_3M_T1/LO_3M_T1<2.2)",
        "_TL3M": "{Init} &(HI_3M_T1/LO_3M_T1<1.36) & (Volume > 1.23*Volume_3M_P90)& (ROE5Y>0.07) & (PE<10.0) & (PB < 1.9) & (FSCORE > 1.0) & (NP_P0 > 1.2*NP_P1) & (PCF>0.4) & (NP_P1 > 0) & (PE >3.0)",
        "_BuySupport": "{Init} &(Close >1.1300000000000001* Sup_1Y) & (LO_3M_T1 < 1.42*Sup_1Y) &( Close < 1.25*LO_3M_T1)  & (PE < 8.0) & (PB <4.6000000000000005) & (PCF <30.200000000000003) & (PCF >0.6000000000000001)  &  ((Cash_P0/ (LtDebt_P0+1) > 0.015)|abs(IntCov_P0 > 7.0)) & (CF_OA_5Y/OShares> 8000.0) & (ROE_Min5Y > 0.105) & (ICB_Code != 2353)",
        "_RSILow30": "{Init} &(D_RSI < 0.3)  & (PE < 7.4)  & (PE>3.8) & (ROE_Min3Y > 0.05) & (PB < 0.85*PB_MA5Y - 0.55*PB_SD5Y) & (PCF > 2.4) & (PCF <27.0) & ((Cash_P0/ (LtDebt_P0+1) > 0.06)|(abs(IntCov_P0) > 3.4)) & (NP_P0 > 0)",
        "_UnderBV": "{Init} &(PB < 1.48) & (FSCORE >= 1.0) & (NP_P0 > 1.32*NP_P1)  & (PCF>1.0)  & (PE >0.0)  & (PCF < 23.0)  & ((NP_P0+NP_P1+NP_P2+NP_P3)/OShares > 1750.0) & (NP_P0/NP_P4 > 1.15)",
        "_SuperGrowth": "{Init} &(PE/((NP_P0/NP_P4 -1)*100) < 0.93) & (ROE_Min5Y > 0.035) &  ((FSCORE>=6.0)) & (NP_P0/NP_P4 > 1.31)  & (NP_P4 >= 0)  & (PCF > 0.9) & (PCF < 14.0) & (CF_OA_5Y/OShares > 8000.0) & (ID_Current -  ID_Release <= 10)",
        "_SurpriseEarning": "{Init} &(PE < 11.5) & (PB < 1.9000000000000001) & (ROE_Min5Y > 0.01) & ((NP_P0 - NP_P4)/NP_P4 > 0.18) & (NP_P0/NP_P1> 1.4000000000000001) & (NP_P1 > 0) & (PCF > 1.0) & (PCF < 16.0) & (CF_OA_5Y/OShares > 9500.0) & ((Cash_P0/ (LtDebt_P0+1) >0.04)|(abs(IntCov_P0) > 1.0))",
        "_Conservative": "(Volume_1M_P50*Price>3e+8) & (time>='2014-01-01') & (time<='2026-01-01') & (((CF_OA_5Y + CF_Invest_5Y )/5)/(OShares*Price + LtDebt_P0) > 0.045) & ((CF_OA_P0+CF_OA_P1+CF_OA_P2+CF_OA_P3 + CF_Invest_P0 + CF_Invest_P1+ CF_Invest_P2+CF_Invest_P3)/(OShares*Price + LtDebt_P0)>0.11) & ((Cash_P0/ (LtDebt_P0+1) > 0.098)|(abs(IntCov_P0) > 6.0))  & (NP_P0 /NP_P1> 1.1) & (NP_P1>0) & (PE >1.2000000000000002) & (ROE_Min3Y > 0.09) & (PE < 21.0) & (NP_P0/NP_P4 > 1.05)",
        "_BullDvg": "{Init} &(D_RSI / D_RSI_T1 > 0.8) & (D_RSI > 0.46) & (D_RSI < 0.86) & (D_RSI_Min3M < 0.44) & (D_RSI_Min1W > 0.08) & (D_RSI_Min1W/D_RSI_Min3M > 1.08) &  (D_RSI_Min1W_Close/D_RSI_Min3M_Close < 1.8)  & (FSCORE > 4.0) & (PE< 11.8) &  (PE>3.4) & (PB < 3.1)  & (ROE_Min5Y > 0.025) & (PCF <22.5) & (PCF>1.0) &  ((Cash_P0/ (LtDebt_P0+1) > 0.064)|(abs(IntCov_P0) > 7.0)) & (CF_OA_5Y/OShares> 7800.0) & (NP_P0/NP_P4 >=1.3)",
        "_VolMax1Y": "{Init} &(Close > 1.01*Volume_Max1Y_High) & (Close_T1W < 1.08*Volume_Max1Y_High) & (Volume > 0.85*Volume_3M_P50) & (PE >1.0) & (PE < 10.8) & (PB<4.0) & (PCF > 1.0) & (((NP_P0 > 1.15*NP_P1)& (PCF < 15.8) & (ROE_Min3Y > 0.025)) | ((((NP_P0 - NP_P4)/abs(NP_P4) > 1.1)) & (PCF < 11.4)))  & (ID_Current-Volume_Max1Y_ID<=150.0)  & (Volume_Max1Y_High/LO_3M_T1 < 1.45) & (FSCORE > 3.0)",
    }

    if args.input:
        with open(args.input, 'r', encoding='utf-8') as f:
            data = json.load(f)
    else:
        data = sample

    records = parse_expressions(data)
    bounds = compute_bounds(records)

    outdir = Path(args.outdir)
    outdir.mkdir(parents=True, exist_ok=True)

    df_rules = pd.DataFrame(records).sort_values(['class', 'expr', 'op', 'x']).reset_index(drop=True)
    df_bounds = pd.DataFrame(bounds).sort_values('expr').reset_index(drop=True)

    rules_path = outdir / 'kept_clauses.csv'
    bounds_path = outdir / 'expr_bounds.csv'
    df_rules.to_csv(rules_path, index=False)
    df_bounds.to_csv(bounds_path, index=False)

    print(f'✅ Saved: {rules_path}')
    print(f'✅ Saved: {bounds_path}')


if __name__ == '__main__':
    main()
