#!/bin/bash


echo "Cron is called."
set -a
source /workspace/kaffa_v2/cron/.env
set +a

export PYTHONPATH=$PYTHONPATH:/workspace/kaffa_v2
source /workspace/py310/bin/activate

# Remove all previous file before starting cron
rm -rf /workspace/kaffa_v2/report/assets/*.html
rm -rf /workspace/kaffa_v2/report/assets/*.pdf

# Clear all old cache
/workspace/py310/bin/python /workspace/kaffa_v2/cron/clear_cache.py

export PYTHONPATH=$PYTHONPATH:/workspace/kaffa_v2
/workspace/py310/bin/python /workspace/kaffa_v2/report/build_report.py
echo "build_report.py done"

echo "Done"

